#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据导入窗口修复验证脚本

测试修复后的字段映射显示功能，确保在选择Sheet后能正确显示字段映射表格内容。
"""

import sys
import os
import time
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
from src.utils.log_config import setup_logger

def test_unified_import_window():
    """测试统一数据导入窗口的修复"""
    logger = setup_logger(__name__)
    
    try:
        logger.info("开始测试统一数据导入窗口修复...")
        
        # 创建QApplication
        app = QApplication(sys.argv)
        
        # 导入相关模块
        from src.gui.unified_data_import_window import UnifiedDataImportWindow
        from src.modules.system_config.config_manager import ConfigManager
        from src.modules.data_storage.database_manager import DatabaseManager
        
        # 初始化依赖
        config_manager = ConfigManager()
        db_manager = DatabaseManager(db_path=None, config_manager=config_manager)
        
        # 创建统一数据导入窗口
        import_window = UnifiedDataImportWindow(
            parent=None,
            db_manager=db_manager,
            config_manager=config_manager
        )
        
        logger.info("✅ 统一数据导入窗口创建成功")
        
        # 检查mapping_tab是否存在
        if hasattr(import_window, 'mapping_tab'):
            logger.info("✅ mapping_tab属性存在")
            
            # 检查_smart_save_user_modified_configs方法是否存在
            if hasattr(import_window.mapping_tab, '_smart_save_user_modified_configs'):
                logger.info("✅ mapping_tab._smart_save_user_modified_configs方法存在")
            else:
                logger.error("❌ mapping_tab._smart_save_user_modified_configs方法不存在")
                
            # 检查其他相关方法
            methods_to_check = [
                '_force_save_all_field_configs',
                '_save_mapping_config_immediately',
                'get_current_mapping_config',
                'save_current_config'
            ]
            
            for method_name in methods_to_check:
                if hasattr(import_window.mapping_tab, method_name):
                    logger.info(f"✅ mapping_tab.{method_name}方法存在")
                else:
                    logger.warning(f"⚠️ mapping_tab.{method_name}方法不存在")
        else:
            logger.error("❌ mapping_tab属性不存在")
            return False
        
        # 测试加载默认文件
        test_file_path = "data/工资表/2025年5月份正式工工资（报财务)  最终版.xls"
        if os.path.exists(test_file_path):
            logger.info(f"测试文件存在: {test_file_path}")
            
            # 显示窗口
            import_window.show()
            
            # 设置定时器来模拟用户操作
            def test_sheet_selection():
                try:
                    logger.info("开始测试Sheet选择功能...")
                    
                    # 检查是否有Sheet管理组件
                    if hasattr(import_window, 'sheet_management_widget'):
                        sheet_widget = import_window.sheet_management_widget
                        logger.info("✅ Sheet管理组件存在")
                        
                        # 检查是否有Sheet树
                        if hasattr(sheet_widget, 'sheet_tree'):
                            sheet_tree = sheet_widget.sheet_tree
                            logger.info(f"✅ Sheet树存在，顶级项目数: {sheet_tree.topLevelItemCount()}")
                            
                            # 如果有Sheet项目，尝试选择第一个
                            if sheet_tree.topLevelItemCount() > 0:
                                first_item = sheet_tree.topLevelItem(0)
                                sheet_tree.setCurrentItem(first_item)
                                logger.info("✅ 已选择第一个Sheet项目")
                                
                                # 等待一下让信号处理完成
                                QTimer.singleShot(1000, test_mapping_display)
                            else:
                                logger.warning("⚠️ 没有找到Sheet项目")
                                app.quit()
                        else:
                            logger.error("❌ Sheet树不存在")
                            app.quit()
                    else:
                        logger.error("❌ Sheet管理组件不存在")
                        app.quit()
                        
                except Exception as e:
                    logger.error(f"测试Sheet选择功能失败: {e}")
                    logger.error(traceback.format_exc())
                    app.quit()
            
            def test_mapping_display():
                try:
                    logger.info("开始测试字段映射显示功能...")
                    
                    # 检查映射表格是否有内容
                    if hasattr(import_window.mapping_tab, 'mapping_table'):
                        mapping_table = import_window.mapping_tab.mapping_table
                        row_count = mapping_table.rowCount()
                        col_count = mapping_table.columnCount()
                        
                        logger.info(f"✅ 映射表格存在，行数: {row_count}, 列数: {col_count}")
                        
                        if row_count > 0:
                            logger.info("✅ 映射表格有内容")
                            
                            # 检查第一行的内容
                            for col in range(min(col_count, 3)):  # 只检查前3列
                                item = mapping_table.item(0, col)
                                if item:
                                    logger.info(f"第0行第{col}列内容: {item.text()}")
                                else:
                                    widget = mapping_table.cellWidget(0, col)
                                    if widget:
                                        logger.info(f"第0行第{col}列是组件: {type(widget).__name__}")
                        else:
                            logger.warning("⚠️ 映射表格没有内容")
                    else:
                        logger.error("❌ 映射表格不存在")
                    
                    # 测试完成，退出应用
                    QTimer.singleShot(2000, app.quit)
                    
                except Exception as e:
                    logger.error(f"测试字段映射显示功能失败: {e}")
                    logger.error(traceback.format_exc())
                    app.quit()
            
            # 延迟启动测试
            QTimer.singleShot(2000, test_sheet_selection)
            
            # 运行应用
            app.exec_()
            
        else:
            logger.warning(f"测试文件不存在: {test_file_path}")
            return False
        
        logger.info("✅ 统一数据导入窗口修复验证完成")
        return True
        
    except Exception as e:
        logger.error(f"测试统一数据导入窗口修复失败: {e}")
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    success = test_unified_import_window()
    if success:
        print("✅ 测试通过")
        sys.exit(0)
    else:
        print("❌ 测试失败")
        sys.exit(1)
