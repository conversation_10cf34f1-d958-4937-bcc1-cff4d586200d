"""
字段类型隔离修复测试

测试方案一的实施效果，验证字段类型下拉框不再显示异常类型
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import re
from src.modules.data_import.formatting_engine import get_formatting_engine, FormattingEngine


def test_field_type_isolation():
    """测试字段类型隔离机制"""
    print("🧪 测试字段类型隔离修复...")
    
    # 获取格式化引擎实例
    engine = get_formatting_engine()
    print("✅ 获取格式化引擎实例成功")
    
    # 测试1：验证内置类型和临时类型的分离
    print("\n📋 测试1：验证内置类型和临时类型的分离")
    
    # 检查是否有新的方法
    assert hasattr(engine, 'get_builtin_field_types'), "缺少 get_builtin_field_types 方法"
    assert hasattr(engine, 'get_temporary_field_types'), "缺少 get_temporary_field_types 方法"
    assert hasattr(engine, 'clear_temporary_field_types'), "缺少 clear_temporary_field_types 方法"
    print("✅ 新增方法检查通过")
    
    # 获取内置类型
    builtin_types = engine.get_builtin_field_types()
    print(f"✅ 内置字段类型数量: {len(builtin_types)}")
    
    # 验证内置类型包含预期的类型
    expected_builtin_types = [
        'salary_float', 'employee_id_string', 'name_string', 
        'date_string', 'id_number_string', 'code_string',
        'float', 'integer', 'text_string', 'personnel_category_code'
    ]
    
    for expected_type in expected_builtin_types:
        assert expected_type in builtin_types, f"缺少内置类型: {expected_type}"
    print("✅ 内置类型完整性检查通过")
    
    # 测试2：验证类型名称验证机制
    print("\n📋 测试2：验证类型名称验证机制")
    
    # 测试有效的类型名称
    valid_type_names = ['valid_type', 'custom_type_1', 'user_defined_type']
    for type_name in valid_type_names:
        assert engine._validate_field_type_name(type_name), f"类型名称应该有效: {type_name}"
    print("✅ 有效类型名称验证通过")
    
    # 测试无效的类型名称（应该被拒绝）
    invalid_type_names = [
        '2025公积金',  # 包含中文和年份
        '卫生费',      # 纯中文
        'salary2025',  # 包含年份
        'EmployeeName', # 驼峰命名
        '1invalid',    # 数字开头
        '',            # 空字符串
        None           # None值
    ]
    
    for type_name in invalid_type_names:
        if type_name is not None:
            assert not engine._validate_field_type_name(type_name), f"类型名称应该无效: {type_name}"
        else:
            assert not engine._validate_field_type_name(type_name), "None值应该无效"
    print("✅ 无效类型名称验证通过")
    
    # 测试3：验证临时类型注册和清理
    print("\n📋 测试3：验证临时类型注册和清理")
    
    # 尝试注册一个有效的临时类型
    valid_temp_type = {
        "name": "测试临时类型",
        "description": "用于测试的临时类型",
        "rule_type": "string"
    }
    
    # 注册临时类型
    engine.register_field_type("test_temp_type", valid_temp_type)
    
    # 验证临时类型被正确注册
    temp_types = engine.get_temporary_field_types()
    assert "test_temp_type" in temp_types, "临时类型应该被正确注册"
    print("✅ 临时类型注册成功")
    
    # 清理临时类型
    engine.clear_temporary_field_types()
    
    # 验证临时类型被清理
    temp_types_after_clear = engine.get_temporary_field_types()
    assert len(temp_types_after_clear) == 0, "临时类型应该被完全清理"
    print("✅ 临时类型清理成功")
    
    # 测试4：验证内置类型保护
    print("\n📋 测试4：验证内置类型保护")
    
    # 尝试覆盖内置类型
    override_attempt = {
        "name": "覆盖尝试",
        "description": "尝试覆盖内置类型",
        "rule_type": "string"
    }
    
    # 尝试覆盖 salary_float 类型
    engine.register_field_type("salary_float", override_attempt)
    
    # 验证内置类型没有被覆盖
    builtin_types_after_attempt = engine.get_builtin_field_types()
    original_salary_float = builtin_types["salary_float"]
    current_salary_float = builtin_types_after_attempt["salary_float"]
    
    assert current_salary_float["name"] == original_salary_float["name"], "内置类型不应该被覆盖"
    print("✅ 内置类型保护机制正常")
    
    # 测试5：验证合并视图更新
    print("\n📋 测试5：验证合并视图更新")
    
    # 注册一个新的临时类型
    new_temp_type = {
        "name": "新临时类型",
        "description": "新注册的临时类型",
        "rule_type": "string"
    }
    
    engine.register_field_type("new_temp_type", new_temp_type)
    
    # 验证合并视图包含所有类型
    merged_types = engine.get_field_types()
    assert "salary_float" in merged_types, "合并视图应该包含内置类型"
    assert "new_temp_type" in merged_types, "合并视图应该包含临时类型"
    
    # 验证类型数量正确
    expected_total = len(builtin_types) + 1  # 内置类型 + 1个临时类型
    assert len(merged_types) == expected_total, f"合并视图类型数量应该为 {expected_total}"
    print("✅ 合并视图更新机制正常")
    
    print("\n🎉 所有测试通过！字段类型隔离修复成功！")


def test_field_type_validation_edge_cases():
    """测试字段类型验证的边界情况"""
    print("\n🧪 测试字段类型验证的边界情况...")
    
    engine = get_formatting_engine()
    
    # 测试边界情况
    edge_cases = [
        ("a" * 100, True),      # 长名称
        ("_", True),             # 单个下划线
        ("a", True),             # 单个字母
        ("a1", True),            # 字母+数字
        ("a_1", True),           # 字母+下划线+数字
        ("1a", False),           # 数字开头
        ("a-1", False),          # 包含连字符
        ("a.1", False),          # 包含点号
        ("a 1", False),          # 包含空格
        ("a\t1", False),         # 包含制表符
        ("a\n1", False),         # 包含换行符
    ]
    
    for type_name, expected_valid in edge_cases:
        actual_valid = engine._validate_field_type_name(type_name)
        assert actual_valid == expected_valid, f"类型名称 '{type_name}' 验证结果不符合预期: 期望 {expected_valid}, 实际 {actual_valid}"
    
    print("✅ 边界情况测试通过")


def test_database_field_name_detection():
    """测试数据库字段名检测"""
    print("\n🧪 测试数据库字段名检测...")
    
    # 这个方法在UnifiedMappingConfigWidget类中，我们直接测试正则表达式逻辑
    import re
    
    def is_likely_temporary_type(type_id: str) -> bool:
        """模拟数据库字段名检测逻辑"""
        if not type_id:
            return True
        
        # 检查是否包含中文字符
        if any('\u4e00' <= char <= '\u9fff' for char in type_id):
            return True
        
        # 检查是否包含4位数字（可能是年份）
        if re.search(r'\d{4}', type_id):
            return True
        
        # 检查是否为常见的数据库字段名格式
        if re.match(r'^[A-Z][a-z]+', type_id):  # 驼峰命名
            return True
        
        if re.match(r'^\d+[A-Za-z]+', type_id):  # 数字开头
            return True
        
        if re.match(r'^[A-Za-z]+\d{4}', type_id):  # 字母+4位数字
            return True
        
        return False
    
    # 测试数据库字段名模式
    db_field_patterns = [
        ("EmployeeName", True),      # 驼峰命名
        ("employeeName", False),     # 小驼峰命名（允许）
        ("2025Salary", True),        # 数字开头
        ("Salary2025", True),        # 字母+年份
        ("salary_2025", False),      # 下划线分隔（允许）
        ("salary2025", False),       # 字母+年份（允许）
        ("validType", False),        # 驼峰命名（允许）
    ]
    
    for type_name, should_be_detected in db_field_patterns:
        is_detected = is_likely_temporary_type(type_name)
        if should_be_detected:
            print(f"  ⚠️  类型名称 '{type_name}' 被检测为可能的数据库字段名")
        else:
            print(f"  ✅ 类型名称 '{type_name}' 通过检测")
    
    print("✅ 数据库字段名检测测试完成")


if __name__ == "__main__":
    try:
        test_field_type_isolation()
        test_field_type_validation_edge_cases()
        test_database_field_name_detection()
        
        print("\n🎯 测试总结:")
        print("✅ 字段类型隔离机制正常工作")
        print("✅ 类型名称验证机制有效")
        print("✅ 临时类型清理机制正常")
        print("✅ 内置类型保护机制正常")
        print("✅ 合并视图更新机制正常")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
