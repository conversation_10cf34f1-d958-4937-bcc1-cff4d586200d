#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 [方案B实施] 增强配置版本控制测试脚本

测试内容：
1. 配置版本控制机制
2. 配置状态跟踪
3. 智能保存机制
4. 配置冲突检测
5. 配置优先级计算
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_config_version_control():
    """测试配置版本控制机制"""
    print("🔧 [方案B测试] 开始测试配置版本控制机制...")
    
    try:
        # 导入ConfigSyncManager
        from src.modules.data_import.config_sync_manager import ConfigSyncManager
        
        # 创建测试实例
        config_manager = ConfigSyncManager()
        
        # 测试配置版本生成
        print("  📝 测试配置版本生成...")
        version1 = config_manager._generate_config_version()
        version2 = config_manager._generate_config_version()
        
        print(f"    版本1: {version1}")
        print(f"    版本2: {version2}")
        print(f"    版本唯一性: {version1 != version2}")
        
        # 测试配置稳定性分数计算
        print("  📝 测试配置稳定性分数计算...")
        test_config = {
            'field_type': 'salary_float',
            'data_type': 'DECIMAL(10,2)',
            'last_modified': time.time()
        }
        
        test_state = {
            'user_modification_count': 3,
            'last_user_modification': time.time() - 3600  # 1小时前
        }
        
        stability_score = config_manager._calculate_config_stability_score(test_config, test_state)
        print(f"    配置稳定性分数: {stability_score:.2f}")
        
        # 测试配置优先级计算
        print("  📝 测试配置优先级计算...")
        ui_config = {
            'field_type': 'integer',
            'data_type': 'INT',
            'last_modified': time.time(),
            'config_source': 'user_modified'
        }
        
        saved_config = {
            'field_type': 'salary_float',
            'data_type': 'DECIMAL(10,2)',
            'last_modified': time.time() - 86400,  # 1天前
            'config_source': 'smart_inference'
        }
        
        ui_priority = config_manager._calculate_config_priority(ui_config, test_state, is_ui=True)
        saved_priority = config_manager._calculate_config_priority(saved_config, test_state, is_ui=False)
        
        print(f"    UI配置优先级: {ui_priority:.2f}")
        print(f"    保存配置优先级: {saved_priority:.2f}")
        print(f"    UI配置获胜: {ui_priority > saved_priority}")
        
        print("  ✅ 配置版本控制机制测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 配置版本控制机制测试失败: {e}")
        return False

def test_config_conflict_resolution():
    """测试配置冲突解决机制"""
    print("🔧 [方案B测试] 开始测试配置冲突解决机制...")
    
    try:
        from src.modules.data_import.config_sync_manager import ConfigSyncManager
        
        config_manager = ConfigSyncManager()
        
        # 测试配置冲突解决
        print("  📝 测试配置冲突解决...")
        
        ui_config = {
            'field_type': 'integer',
            'data_type': 'INT',
            'last_modified': time.time(),
            'config_source': 'user_modified'
        }
        
        saved_config = {
            'field_type': 'salary_float',
            'data_type': 'DECIMAL(10,2)',
            'last_modified': time.time() - 86400,
            'config_source': 'smart_inference'
        }
        
        resolved_config = config_manager.resolve_config_conflicts(
            'test_table', 'test_field', ui_config, saved_config
        )
        
        print(f"    冲突解决结果: {resolved_config.get('field_type')}")
        print(f"    使用UI配置: {resolved_config == ui_config}")
        
        print("  ✅ 配置冲突解决机制测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 配置冲突解决机制测试失败: {e}")
        return False

def test_field_state_tracking():
    """测试字段状态跟踪机制"""
    print("🔧 [方案B测试] 开始测试字段状态跟踪机制...")
    
    try:
        # 模拟字段状态跟踪
        field_state_tracking = {}
        
        # 模拟用户修改字段
        excel_field = "序号"
        current_time = time.time()
        
        field_config = {
            'field_type': 'integer',
            'data_type': 'INT',
            'is_required': False,
            'last_modified': current_time,
            'user_modified': True,
            'config_source': 'user_modified'
        }
        
        # 更新字段状态
        if excel_field not in field_state_tracking:
            field_state_tracking[excel_field] = {}
        
        field_state_tracking[excel_field].update({
            'last_user_modification': current_time,
            'user_modification_count': field_state_tracking[excel_field].get('user_modification_count', 0) + 1,
            'current_config': field_config.copy(),
            'last_modified': current_time
        })
        
        print(f"  📝 字段状态跟踪: {excel_field}")
        print(f"    用户修改次数: {field_state_tracking[excel_field]['user_modification_count']}")
        print(f"    最后修改时间: {field_state_tracking[excel_field]['last_user_modification']}")
        print(f"    当前配置: {field_state_tracking[excel_field]['current_config']['field_type']}")
        
        print("  ✅ 字段状态跟踪机制测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 字段状态跟踪机制测试失败: {e}")
        return False

def test_smart_save_mechanism():
    """测试智能保存机制"""
    print("🔧 [方案B测试] 开始测试智能保存机制...")
    
    try:
        # 模拟智能保存逻辑
        field_state_tracking = {
            "序号": {
                'last_user_modification': time.time() - 120,  # 2分钟前
                'user_modification_count': 2
            },
            "姓名": {
                'last_user_modification': time.time() - 600,  # 10分钟前
                'user_modification_count': 1
            },
            "工号": {
                'last_user_modification': time.time() - 3600,  # 1小时前
                'user_modification_count': 0
            }
        }
        
        current_time = time.time()
        recent_modifications = []
        
        # 检查最近5分钟内的修改
        for field, state in field_state_tracking.items():
            last_mod = state['last_user_modification']
            if (current_time - last_mod) < 300:  # 5分钟
                recent_modifications.append(field)
        
        print(f"  📝 智能保存检测结果:")
        print(f"    总字段数: {len(field_state_tracking)}")
        print(f"    最近修改字段: {recent_modifications}")
        print(f"    需要保存字段数: {len(recent_modifications)}")
        
        # 模拟保存逻辑
        if recent_modifications:
            print(f"    ✅ 将保存字段: {', '.join(recent_modifications)}")
        else:
            print(f"    ℹ️ 没有需要保存的字段")
        
        print("  ✅ 智能保存机制测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 智能保存机制测试失败: {e}")
        return False

def test_config_enhancement():
    """测试配置增强机制"""
    print("🔧 [方案B测试] 开始测试配置增强机制...")
    
    try:
        # 模拟配置增强过程
        saved_configs = {
            "序号": {
                'field_type': 'integer',
                'data_type': 'INT',
                'config_source': 'user_modified',
                'last_modified': time.time()
            },
            "姓名": {
                'field_type': 'name_string',
                'data_type': 'VARCHAR(100)',
                'config_source': 'smart_inference',
                'last_modified': time.time() - 3600
            }
        }
        
        enhanced_configs = {}
        user_modified_count = 0
        
        for excel_field, field_config in saved_configs.items():
            # 检查配置来源
            if field_config.get('config_source') == 'user_modified':
                user_modified_count += 1
                enhanced_configs[excel_field] = field_config
                print(f"  📝 字段 '{excel_field}' 使用用户修改配置")
            else:
                # 检查是否需要应用智能推断配置
                current_time = time.time()
                last_modified = field_config.get('last_modified', current_time)
                time_diff = current_time - last_modified
                
                if time_diff < 86400:  # 24小时内
                    enhanced_configs[excel_field] = field_config
                    print(f"  📝 字段 '{excel_field}' 使用智能推断配置")
                else:
                    print(f"  📝 字段 '{excel_field}' 配置已过期，跳过")
        
        print(f"  📝 配置增强结果:")
        print(f"    原始配置数: {len(saved_configs)}")
        print(f"    增强配置数: {len(enhanced_configs)}")
        print(f"    用户修改配置数: {user_modified_count}")
        
        print("  ✅ 配置增强机制测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 配置增强机制测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 [方案B实施] 增强配置版本控制测试开始")
    print("=" * 60)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("配置版本控制机制", test_config_version_control()))
    test_results.append(("配置冲突解决机制", test_config_conflict_resolution()))
    test_results.append(("字段状态跟踪机制", test_field_state_tracking()))
    test_results.append(("智能保存机制", test_smart_save_mechanism()))
    test_results.append(("配置增强机制", test_config_enhancement()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("🔧 [方案B实施] 测试结果汇总")
    print("=" * 60)
    
    passed_count = 0
    total_count = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed_count += 1
    
    print("-" * 60)
    print(f"总计: {passed_count}/{total_count} 项测试通过")
    
    if passed_count == total_count:
        print("🎉 所有测试通过！方案B实施成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要检查实施情况")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
