"""
格式化规则引擎
提供可扩展的字段格式化处理框架
"""

import re
import json
from typing import Any, Dict, List, Optional, Callable, Union
from datetime import datetime
import pandas as pd
from loguru import logger
from abc import ABC, abstractmethod


class FormattingRule(ABC):
    """格式化规则基类"""
    
    def __init__(self, rule_name: str, rule_config: Dict[str, Any] = None):
        """
        初始化格式化规则
        
        Args:
            rule_name: 规则名称
            rule_config: 规则配置参数
        """
        self.rule_name = rule_name
        self.rule_config = rule_config or {}
    
    @abstractmethod
    def apply(self, value: Any) -> Any:
        """
        应用格式化规则
        
        Args:
            value: 要格式化的值
            
        Returns:
            格式化后的值
        """
        pass
    
    @abstractmethod
    def validate(self, value: Any) -> bool:
        """
        验证值是否符合规则要求
        
        Args:
            value: 要验证的值
            
        Returns:
            是否有效
        """
        pass
    
    def get_description(self) -> str:
        """获取规则描述"""
        return f"{self.rule_name}: {self.rule_config}"


class NumberFormattingRule(FormattingRule):
    """数值格式化规则"""
    
    def apply(self, value: Any) -> str:
        """应用数值格式化"""
        if pd.isna(value) or str(value).strip() == "":
            return ""
        
        try:
            num = float(value)
            
            # 获取配置
            decimal_places = self.rule_config.get("decimal_places", 2)
            thousands_separator = self.rule_config.get("thousands_separator", True)
            negative_format = self.rule_config.get("negative_format", "minus")  # minus, parentheses, red
            
            # 格式化
            if thousands_separator:
                formatted = f"{num:,.{decimal_places}f}"
            else:
                formatted = f"{num:.{decimal_places}f}"
            
            # 处理负数格式
            if num < 0:
                if negative_format == "parentheses":
                    formatted = f"({formatted.replace('-', '')})"
                elif negative_format == "red":
                    # 这里只返回标记，实际颜色由UI处理
                    formatted = f"[RED]{formatted}[/RED]"
            
            return formatted
            
        except (ValueError, TypeError) as e:
            logger.warning(f"数值格式化失败: {e}")
            return str(value)
    
    def validate(self, value: Any) -> bool:
        """验证是否为有效数值"""
        if pd.isna(value) or str(value).strip() == "":
            return True
        try:
            float(value)
            return True
        except:
            return False


class StringFormattingRule(FormattingRule):
    """字符串格式化规则"""
    
    def apply(self, value: Any) -> str:
        """应用字符串格式化"""
        if pd.isna(value):
            return ""
        
        str_val = str(value)
        
        # 获取配置
        case_format = self.rule_config.get("case", "original")  # original, upper, lower, title
        trim_spaces = self.rule_config.get("trim_spaces", True)
        max_length = self.rule_config.get("max_length", None)
        padding_char = self.rule_config.get("padding_char", " ")
        padding_side = self.rule_config.get("padding_side", "right")  # left, right
        min_length = self.rule_config.get("min_length", None)
        
        # 去除空格
        if trim_spaces:
            str_val = str_val.strip()
        
        # 大小写转换
        if case_format == "upper":
            str_val = str_val.upper()
        elif case_format == "lower":
            str_val = str_val.lower()
        elif case_format == "title":
            str_val = str_val.title()
        
        # 长度限制
        if max_length and len(str_val) > max_length:
            str_val = str_val[:max_length]
        
        # 填充
        if min_length and len(str_val) < min_length:
            padding_needed = min_length - len(str_val)
            if padding_side == "left":
                str_val = (padding_char * padding_needed) + str_val
            else:
                str_val = str_val + (padding_char * padding_needed)
        
        return str_val
    
    def validate(self, value: Any) -> bool:
        """验证字符串"""
        if pd.isna(value):
            return True
        
        str_val = str(value)
        
        # 检查最小长度
        min_length = self.rule_config.get("min_length")
        if min_length and len(str_val.strip()) < min_length:
            return False
        
        # 检查最大长度
        max_length = self.rule_config.get("max_length")
        if max_length and len(str_val) > max_length:
            return False
        
        # 检查正则表达式
        pattern = self.rule_config.get("pattern")
        if pattern:
            if not re.match(pattern, str_val):
                return False
        
        return True


class DateFormattingRule(FormattingRule):
    """日期格式化规则"""
    
    def apply(self, value: Any) -> str:
        """应用日期格式化"""
        if pd.isna(value) or str(value).strip() == "":
            return ""
        
        try:
            # 尝试解析日期
            if isinstance(value, datetime):
                dt = value
            else:
                # 尝试多种日期格式
                date_formats = [
                    "%Y-%m-%d",
                    "%Y/%m/%d",
                    "%d/%m/%Y",
                    "%Y年%m月%d日",
                    "%Y.%m.%d",
                    "%Y%m%d"
                ]
                
                dt = None
                for fmt in date_formats:
                    try:
                        dt = datetime.strptime(str(value).strip(), fmt)
                        break
                    except:
                        continue
                
                if not dt:
                    # 使用pandas的智能解析
                    dt = pd.to_datetime(value)
            
            # 格式化输出
            output_format = self.rule_config.get("format", "%Y-%m-%d")
            return dt.strftime(output_format)
            
        except Exception as e:
            logger.warning(f"日期格式化失败: {e}")
            return str(value)
    
    def validate(self, value: Any) -> bool:
        """验证日期格式"""
        if pd.isna(value) or str(value).strip() == "":
            return True
        
        try:
            pd.to_datetime(value)
            return True
        except:
            return False


class CodeFormattingRule(FormattingRule):
    """代码/ID格式化规则"""
    
    def apply(self, value: Any) -> str:
        """应用代码格式化"""
        if pd.isna(value):
            return ""
        
        # 🔧 智能数值处理 - 解决工号显示问题
        if isinstance(value, (int, float)):
            if value == int(value):  # 整数值（如19986677.0 -> 19986677）
                str_val = str(int(value))
            else:
                str_val = str(value).strip()
        elif isinstance(value, str) and self._is_numeric_string(value):
            # 处理字符串形式的数值（如"19986677.0" -> "19986677"）
            try:
                num_val = float(value)
                if num_val == int(num_val):
                    str_val = str(int(num_val))
                else:
                    str_val = str(num_val)
            except ValueError:
                str_val = str(value).strip()
        else:
            str_val = str(value).strip()
        
        # 获取配置
        prefix = self.rule_config.get("prefix", "")
        suffix = self.rule_config.get("suffix", "")
        min_length = self.rule_config.get("min_length", 0)
        padding_char = self.rule_config.get("padding_char", "0")
        preserve_leading_zeros = self.rule_config.get("preserve_leading_zeros", True)
        
        # 保留前导零
        if preserve_leading_zeros and min_length > 0:
            str_val = str_val.zfill(min_length)
        
        # 添加前缀和后缀
        str_val = f"{prefix}{str_val}{suffix}"
        
        return str_val
    
    def _is_numeric_string(self, value: str) -> bool:
        """检查字符串是否为数值格式"""
        try:
            float(value)
            return True
        except ValueError:
            return False
    
    def validate(self, value: Any) -> bool:
        """验证代码格式"""
        if pd.isna(value):
            return True
        
        str_val = str(value).strip()
        
        # 检查长度
        min_length = self.rule_config.get("min_length")
        if min_length and len(str_val) < min_length:
            return False
        
        # 检查模式
        pattern = self.rule_config.get("pattern")
        if pattern:
            if not re.match(pattern, str_val):
                return False
        
        return True


class CustomFormattingRule(FormattingRule):
    """自定义格式化规则"""
    
    def __init__(self, rule_name: str, rule_config: Dict[str, Any] = None):
        super().__init__(rule_name, rule_config)
        self.format_function = None
        self.validate_function = None
        
        # 尝试从配置中加载自定义函数
        if rule_config:
            self._load_custom_functions()
    
    def _load_custom_functions(self):
        """加载自定义函数"""
        # 这里可以从配置中的Python代码字符串动态创建函数
        format_code = self.rule_config.get("format_code")
        if format_code:
            try:
                # 安全地执行代码（在实际生产环境中需要更严格的安全措施）
                exec_globals = {"pd": pd, "re": re, "datetime": datetime}
                exec(format_code, exec_globals)
                if "format_function" in exec_globals:
                    self.format_function = exec_globals["format_function"]
            except Exception as e:
                logger.error(f"加载自定义格式化函数失败: {e}")
        
        validate_code = self.rule_config.get("validate_code")
        if validate_code:
            try:
                exec_globals = {"pd": pd, "re": re}
                exec(validate_code, exec_globals)
                if "validate_function" in exec_globals:
                    self.validate_function = exec_globals["validate_function"]
            except Exception as e:
                logger.error(f"加载自定义验证函数失败: {e}")
    
    def apply(self, value: Any) -> Any:
        """应用自定义格式化"""
        if self.format_function:
            try:
                return self.format_function(value, self.rule_config)
            except Exception as e:
                logger.warning(f"自定义格式化失败: {e}")
                return value
        return value
    
    def validate(self, value: Any) -> bool:
        """自定义验证"""
        if self.validate_function:
            try:
                return self.validate_function(value, self.rule_config)
            except Exception as e:
                logger.warning(f"自定义验证失败: {e}")
                return False
        return True


class FormattingEngine:
    """格式化引擎"""
    
    def __init__(self):
        """初始化格式化引擎"""
        self.rule_types = {}
        # 🔧 [方案一实施] 区分内置类型和临时类型
        self.builtin_field_types = {}  # 内置字段类型（永久）
        self.temporary_field_types = {}  # 临时字段类型（运行时产生）
        self.field_types = {}  # 兼容性：所有类型的合并视图
        self.custom_rules = {}
        
        # 注册内置规则类型
        self._register_builtin_rules()
        
        # 注册内置字段类型
        self._register_builtin_field_types()
        
        # 🔧 [修复] 初始化时加载自定义字段类型
        self.reload_custom_field_types()
        
        # 🔧 [方案一实施] 初始化合并视图
        self._update_merged_field_types()
    
    def _register_builtin_rules(self):
        """注册内置规则类型"""
        self.register_rule_type("number", NumberFormattingRule)
        self.register_rule_type("string", StringFormattingRule)
        self.register_rule_type("date", DateFormattingRule)
        self.register_rule_type("code", CodeFormattingRule)
        self.register_rule_type("custom", CustomFormattingRule)
    
    def _register_builtin_field_types(self):
        """注册内置字段类型"""
        # 工资金额
        self._register_builtin_field_type("salary_float", {
            "name": "工资金额",
            "description": "金额数值，保留小数，千位分隔符",
            "rule_type": "number",
            "default_config": {
                "decimal_places": 2,
                "thousands_separator": True,
                "negative_format": "minus"
            }
        })
        
        # 工号
        self._register_builtin_field_type("employee_id_string", {
            "name": "工号",
            "description": "员工编号，保留前导零",
            "rule_type": "code",
            "default_config": {
                "min_length": 6,
                "padding_char": "0",
                "preserve_leading_zeros": True
            }
        })
        
        # 姓名
        self._register_builtin_field_type("name_string", {
            "name": "姓名",
            "description": "人员姓名",
            "rule_type": "string",
            "default_config": {
                "trim_spaces": True,
                "case": "original"
            }
        })
        
        # 日期
        self._register_builtin_field_type("date_string", {
            "name": "日期",
            "description": "日期格式",
            "rule_type": "date",
            "default_config": {
                "format": "%Y-%m-%d"
            }
        })
        
        # 身份证
        self._register_builtin_field_type("id_number_string", {
            "name": "身份证号",
            "description": "18位身份证号码",
            "rule_type": "code",
            "default_config": {
                "min_length": 18,
                "pattern": r"^\d{17}[\dXx]$"
            }
        })
        
        # 代码
        self._register_builtin_field_type("code_string", {
            "name": "代码",
            "description": "部门代码等",
            "rule_type": "code",
            "default_config": {
                "preserve_leading_zeros": True
            }
        })
        
        # 浮点数
        self._register_builtin_field_type("float", {
            "name": "浮点数",
            "description": "普通小数",
            "rule_type": "number",
            "default_config": {
                "decimal_places": 4,
                "thousands_separator": False
            }
        })
        
        # 整数
        self._register_builtin_field_type("integer", {
            "name": "整数",
            "description": "整数数值",
            "rule_type": "number",
            "default_config": {
                "decimal_places": 0,
                "thousands_separator": False
            }
        })
        
        # 文本
        self._register_builtin_field_type("text_string", {
            "name": "文本字符串",
            "description": "普通文本",
            "rule_type": "string",
            "default_config": {
                "trim_spaces": True
            }
        })
        
        # 人员类别代码
        self._register_builtin_field_type("personnel_category_code", {
            "name": "人员类别代码",
            "description": "人员类别代码，单位数前补0",
            "rule_type": "code",
            "default_config": {
                "min_length": 2,
                "padding_char": "0",
                "preserve_leading_zeros": True
            }
        })
    
    def register_rule_type(self, rule_type: str, rule_class: type):
        """
        注册规则类型
        
        Args:
            rule_type: 规则类型名称
            rule_class: 规则类
        """
        self.rule_types[rule_type] = rule_class
        logger.info(f"注册规则类型: {rule_type}")
    
    def _register_builtin_field_type(self, field_type: str, field_config: Dict[str, Any]):
        """
        🔧 [方案一实施] 注册内置字段类型
        
        Args:
            field_type: 字段类型标识
            field_config: 字段配置
        """
        self.builtin_field_types[field_type] = field_config
        logger.info(f"🔧 [方案一实施] 注册内置字段类型: {field_type} - {field_config.get('name')}")
    
    def register_field_type(self, field_type: str, field_config: Dict[str, Any]):
        """
        注册字段类型（兼容性方法，现在注册到临时类型）
        
        Args:
            field_type: 字段类型标识
            field_config: 字段配置
        """
        # 🔧 [方案一实施] 验证类型名称格式
        if not self._validate_field_type_name(field_type):
            logger.warning(f"🔧 [方案一实施] 字段类型名称格式无效，跳过注册: {field_type}")
            return
        
        # 🔧 [方案一实施] 检查是否为内置类型
        if field_type in self.builtin_field_types:
            logger.warning(f"🔧 [方案一实施] 尝试覆盖内置字段类型，跳过: {field_type}")
            return
        
        # 🔧 [方案一实施] 注册到临时类型
        self.temporary_field_types[field_type] = field_config
        logger.info(f"🔧 [方案一实施] 注册临时字段类型: {field_type} - {field_config.get('name')}")
        
        # 🔧 [方案一实施] 更新合并视图
        self._update_merged_field_types()
    
    def _validate_field_type_name(self, field_type: str) -> bool:
        """
        🔧 [方案一实施] 验证字段类型名称格式
        
        Args:
            field_type: 字段类型标识
            
        Returns:
            是否为有效的字段类型名称
        """
        # 检查是否为空
        if not field_type or not field_type.strip():
            return False
        
        # 检查是否包含特殊字符（只允许字母、数字、下划线）
        if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', field_type):
            return False
        
        # 检查是否为数据库字段名格式（如包含年份、中文等）
        if re.search(r'[\u4e00-\u9fff]', field_type):  # 包含中文字符
            return False
        
        if re.search(r'\d{4}', field_type):  # 包含4位数字（可能是年份）
            return False
        
        # 检查是否为常见的数据库字段名
        common_db_field_patterns = [
            r'^[A-Z][a-z]+',  # 驼峰命名
            r'^\d+[A-Za-z]+',  # 数字开头
            r'^[A-Za-z]+\d{4}',  # 字母+4位数字
        ]
        
        for pattern in common_db_field_patterns:
            if re.match(pattern, field_type):
                logger.debug(f"🔧 [方案一实施] 字段类型名称可能为数据库字段名: {field_type}")
                return False
        
        return True
    
    def create_rule(self, rule_type: str, rule_name: str, rule_config: Dict[str, Any] = None) -> FormattingRule:
        """
        创建格式化规则实例
        
        Args:
            rule_type: 规则类型
            rule_name: 规则名称
            rule_config: 规则配置
            
        Returns:
            规则实例
        """
        # 🔧 [P2修复] 处理None或空的rule_type
        if not rule_type:
            logger.debug(f"💾 [P2修复] 规则类型为空，使用默认string类型: field={rule_name}")
            rule_type = "string"  # 默认使用字符串类型
        
        rule_class = self.rule_types.get(rule_type)
        if not rule_class:
            logger.warning(f"💾 [P2修复] 未知的规则类型: {rule_type}，尝试使用默认string类型")
            # 尝试使用默认的字符串类型
            rule_class = self.rule_types.get("string")
            if not rule_class:
                logger.error(f"💾 [P2修复] 连默认string类型都不存在，返回None")
                return None
        
        try:
            return rule_class(rule_name, rule_config)
        except Exception as e:
            logger.error(f"💾 [P2修复] 创建规则实例失败: {rule_type}, {e}")
            return None
    
    def format_value(self, value: Any, field_type: str, custom_config: Dict[str, Any] = None) -> Any:
        """
        格式化值
        
        Args:
            value: 要格式化的值
            field_type: 字段类型
            custom_config: 自定义配置（覆盖默认配置）
            
        Returns:
            格式化后的值
        """
        # 获取字段类型配置
        field_info = self.field_types.get(field_type)
        if not field_info:
            # 🔧 [修复] 尝试从字段类型管理器动态加载自定义类型
            field_info = self._load_custom_field_type(field_type)
            if not field_info:
                logger.warning(f"未知的字段类型: {field_type}")
                # 🔧 [修复] 返回字符串类型的值，确保UI组件兼容
                return str(value) if value is not None else ""
        
        # 合并配置
        rule_config = field_info.get("default_config", {}).copy()
        if custom_config:
            rule_config.update(custom_config)
        
        # 创建规则并应用
        rule_type = field_info.get("rule_type")
        rule = self.create_rule(rule_type, field_type, rule_config)
        
        if rule:
            formatted_value = rule.apply(value)
            # 🔧 [修复] 确保返回值为字符串类型，避免UI组件类型错误
            return str(formatted_value) if formatted_value is not None else ""
        
        # 🔧 [修复] 默认返回字符串类型
        return str(value) if value is not None else ""
    
    def validate_value(self, value: Any, field_type: str, custom_config: Dict[str, Any] = None) -> bool:
        """
        验证值
        
        Args:
            value: 要验证的值
            field_type: 字段类型
            custom_config: 自定义配置
            
        Returns:
            是否有效
        """
        # 获取字段类型配置
        field_info = self.field_types.get(field_type)
        if not field_info:
            logger.warning(f"未知的字段类型: {field_type}")
            return True  # 未知类型默认通过
        
        # 合并配置
        rule_config = field_info.get("default_config", {}).copy()
        if custom_config:
            rule_config.update(custom_config)
        
        # 创建规则并验证
        rule_type = field_info.get("rule_type")
        rule = self.create_rule(rule_type, field_type, rule_config)
        
        if rule:
            return rule.validate(value)
        
        return True
    
    def format_dataframe(self, df: pd.DataFrame, field_mappings: Dict[str, str], 
                        custom_configs: Dict[str, Dict[str, Any]] = None) -> pd.DataFrame:
        """
        格式化整个DataFrame
        
        Args:
            df: 要格式化的DataFrame
            field_mappings: 字段到类型的映射 {column_name: field_type}
            custom_configs: 自定义配置 {column_name: config}
            
        Returns:
            格式化后的DataFrame
        """
        formatted_df = df.copy()
        
        for column, field_type in field_mappings.items():
            if column not in formatted_df.columns:
                continue
            
            custom_config = custom_configs.get(column) if custom_configs else None
            
            # 格式化该列
            formatted_df[column] = formatted_df[column].apply(
                lambda x: self.format_value(x, field_type, custom_config)
            )
        
        return formatted_df
    
    def get_field_types(self) -> Dict[str, Dict[str, Any]]:
        """获取所有注册的字段类型"""
        return self.field_types.copy()
    
    # 类型ID映射表（旧ID -> 新ID）
    TYPE_ID_MAPPING = {
        "general": "text_string",
        "salary_amount": "salary_float", 
        "employee_id": "employee_id_string",
        "department": "text_string",
        "year_string": "date_string",
        "month_string": "date_string"
    }
    
    def get_mapped_type_id(self, old_type_id: str) -> str:
        """获取映射后的类型ID"""
        return self.TYPE_ID_MAPPING.get(old_type_id, old_type_id)
    
    def get_legacy_type_mapping(self) -> Dict[str, str]:
        """获取完整的类型映射表"""
        return self.TYPE_ID_MAPPING.copy()
    
    def get_rule_types(self) -> Dict[str, type]:
        """获取所有注册的规则类型"""
        return self.rule_types.copy()
    
    def export_config(self) -> Dict[str, Any]:
        """导出引擎配置"""
        return {
            "field_types": self.field_types,
            "custom_rules": self.custom_rules
        }
    
    def import_config(self, config: Dict[str, Any]):
        """导入引擎配置"""
        if "field_types" in config:
            for field_type, field_config in config["field_types"].items():
                self.register_field_type(field_type, field_config)
        
        if "custom_rules" in config:
            self.custom_rules.update(config["custom_rules"])
    
    def _load_custom_field_type(self, field_type: str) -> Optional[Dict[str, Any]]:
        """
        🔧 [修复] 动态加载自定义字段类型
        
        Args:
            field_type: 字段类型标识
            
        Returns:
            字段类型配置信息
        """
        try:
            from src.modules.data_import.field_type_manager import FieldTypeManager
            
            # 创建字段类型管理器实例
            field_type_manager = FieldTypeManager()
            
            # 获取自定义字段类型
            custom_type_info = field_type_manager.get_field_type(field_type)
            
            if custom_type_info:
                # 将自定义类型注册到格式化引擎
                self.register_field_type(field_type, custom_type_info)
                logger.info(f"🔧 [修复] 动态加载自定义字段类型: {field_type}")
                return custom_type_info
                
        except Exception as e:
            logger.error(f"🔧 [修复] 动态加载自定义字段类型失败: {field_type}, 错误: {e}")
        
        return None
    
    def reload_custom_field_types(self):
        """
        🔧 [方案一实施] 重新加载所有自定义字段类型
        """
        try:
            from src.modules.data_import.field_type_manager import FieldTypeManager
            
            field_type_manager = FieldTypeManager()
            custom_types = field_type_manager.list_custom_field_types()
            
            # 🔧 [方案一实施] 清理之前的自定义类型（从临时类型中移除）
            old_custom_types = [tid for tid in self.temporary_field_types.keys() 
                              if tid.startswith('custom_') or 'custom' in tid]
            for old_type_id in old_custom_types:
                if old_type_id in self.temporary_field_types:
                    del self.temporary_field_types[old_type_id]
                    logger.debug(f"🔧 [方案一实施] 清理旧的自定义字段类型: {old_type_id}")
            
            # 🔧 [方案一实施] 重新加载自定义类型
            loaded_count = 0
            for custom_type in custom_types:
                type_id = custom_type.get("id")
                if type_id:
                    # 🔧 [方案一实施] 使用新的注册方法，会自动验证和分类
                    self.register_field_type(type_id, custom_type)
                    loaded_count += 1
                    logger.debug(f"🔧 [方案一实施] 重新加载自定义字段类型: {type_id}")
            
            # 🔧 [方案一实施] 更新合并视图
            self._update_merged_field_types()
            
            logger.info(f"🔧 [方案一实施] 重新加载了 {loaded_count} 个自定义字段类型")
            
        except Exception as e:
            logger.error(f"🔧 [方案一实施] 重新加载自定义字段类型失败: {e}")
    
    def _update_merged_field_types(self):
        """
        🔧 [方案一实施] 更新合并的字段类型视图
        """
        self.field_types = {}
        self.field_types.update(self.builtin_field_types)
        self.field_types.update(self.temporary_field_types)
        logger.debug(f"🔧 [方案一实施] 更新合并视图: 内置类型 {len(self.builtin_field_types)} 个, 临时类型 {len(self.temporary_field_types)} 个")
    
    def get_builtin_field_types(self) -> Dict[str, Dict[str, Any]]:
        """
        🔧 [方案一实施] 获取内置字段类型（只读）
        
        Returns:
            内置字段类型字典
        """
        return self.builtin_field_types.copy()
    
    def get_temporary_field_types(self) -> Dict[str, Dict[str, Any]]:
        """
        🔧 [方案一实施] 获取临时字段类型（只读）
        
        Returns:
            临时字段类型字典
        """
        return self.temporary_field_types.copy()
    
    def clear_temporary_field_types(self):
        """
        🔧 [方案一实施] 清理所有临时字段类型
        
        用于sheet切换时清理运行时产生的类型
        """
        cleared_count = len(self.temporary_field_types)
        self.temporary_field_types.clear()
        self._update_merged_field_types()
        logger.info(f"🔧 [方案一实施] 已清理 {cleared_count} 个临时字段类型")


# 单例模式
_engine_instance = None

def get_formatting_engine() -> FormattingEngine:
    """获取格式化引擎单例"""
    global _engine_instance
    if _engine_instance is None:
        _engine_instance = FormattingEngine()
    return _engine_instance