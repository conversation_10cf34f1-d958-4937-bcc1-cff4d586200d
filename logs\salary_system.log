2025-09-04 10:46:38.083 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-09-04 10:46:38.083 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-09-04 10:46:38.083 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-09-04 10:46:38.083 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-09-04 10:46:38.083 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-09-04 10:46:38.083 | INFO     | src:<module>:20 | 月度工资异动处理系统 v2.0.0-refactored 初始化完成
2025-09-04 10:46:41.202 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-09-04 10:46:41.217 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-09-04 10:46:41.217 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-09-04 10:46:41.217 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-09-04 10:46:41.217 | INFO     | __main__:setup_app_logging:423 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-09-04 10:46:41.217 | INFO     | __main__:main:487 | 初始化核心管理器...
2025-09-04 10:46:41.217 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-04 10:46:41.217 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-04 10:46:41.217 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-04 10:46:41.217 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-04 10:46:41.217 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-09-04 10:46:41.233 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-09-04 10:46:41.233 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-09-04 10:46:41.233 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-04 10:46:41.233 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-09-04 10:46:41.233 | INFO     | __main__:main:492 | 核心管理器初始化完成。
2025-09-04 10:46:41.233 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-09-04 10:46:41.233 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-09-04 10:46:41.233 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-04 10:46:41.249 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-09-04 10:46:41.249 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-09-04 10:46:41.249 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-09-04 10:46:41.249 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-09-04 10:46:41.249 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11903 | 🔧 [P2-3] 错误恢复策略注册完成
2025-09-04 10:46:41.249 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-04 10:46:41.249 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11758 | 🔧 [P2-3] 错误处理机制设置完成
2025-09-04 10:46:41.249 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11796 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-09-04 10:46:41.420 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-09-04 10:46:41.420 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-09-04 10:46:41.436 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-09-04 10:46:41.452 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:154 | 使用已存在的配置文件: state\data\field_mappings.json
2025-09-04 10:46:41.452 | INFO     | src.modules.data_import.config_sync_manager:__init__:82 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-09-04 10:46:41.452 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-09-04 10:46:41.452 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-04 10:46:41.467 | INFO     | src.core.field_mapping_manager:_load_config:89 | 🔧 [P3优化] 字段映射配置加载成功
2025-09-04 10:46:41.483 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:154 | 使用已存在的配置文件: state\data\field_mappings.json
2025-09-04 10:46:41.483 | INFO     | src.modules.data_import.config_sync_manager:__init__:82 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-09-04 10:46:41.483 | INFO     | src.modules.format_management.field_registry:__init__:99 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-09-04 10:46:41.483 | INFO     | src.core.unified_mapping_service:__init__:45 | UnifiedMappingService 初始化完成
2025-09-04 10:46:41.483 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-04 10:46:41.483 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-04 10:46:41.483 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-09-04 10:46:41.483 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-04 10:46:41.483 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-09-04 10:46:41.483 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 47.0ms
2025-09-04 10:46:41.499 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-09-04 10:46:41.514 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-09-04 10:46:41.514 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-09-04 10:46:41.514 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-09-04 10:46:41.514 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-09-04 10:46:41.514 | INFO     | src.gui.prototype.prototype_main_window:__init__:3650 | 🚀 性能管理器已集成
2025-09-04 10:46:41.514 | INFO     | src.gui.prototype.prototype_main_window:__init__:3652 | ✅ 新架构集成成功！
2025-09-04 10:46:41.514 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3765 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-09-04 10:46:41.514 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3730 | ✅ 新架构事件监听器设置完成
2025-09-04 10:46:41.514 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-09-04 10:46:41.514 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-09-04 10:46:41.514 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-09-04 10:46:41.743 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2726 | 菜单栏创建完成
2025-09-04 10:46:41.759 | INFO     | src.gui.prototype.prototype_main_window:__init__:2701 | 菜单栏管理器初始化完成
2025-09-04 10:46:41.759 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-09-04 10:46:41.759 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5458 | 管理器设置完成，包含增强版表头管理器
2025-09-04 10:46:41.759 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5463 | 🔧 开始应用窗口级Material Design样式...
2025-09-04 10:46:41.759 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-09-04 10:46:41.759 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-09-04 10:46:41.775 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5470 | ✅ 窗口级样式应用成功
2025-09-04 10:46:41.775 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5511 | ✅ 响应式样式监听设置完成
2025-09-04 10:46:41.775 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-04 10:46:41.775 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-04 10:46:41.775 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-04 10:46:41.775 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-09-04 10:46:41.775 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-09-04 10:46:41.775 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-09-04 10:46:41.806 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-04 10:46:41.806 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-04 10:46:41.806 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_execute_salary_data_load:1821 | 动态加载了 1 个月份的工资数据导航
2025-09-04 10:46:41.806 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-09-04 10:46:41.806 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-04 10:46:41.806 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > unknown', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-09-04 10:46:41.806 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 8个展开项
2025-09-04 10:46:41.806 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > unknown', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-09-04 10:46:41.822 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-09-04 10:46:41.828 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_state:165 | 导航状态加载成功
2025-09-04 10:46:41.829 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-09-04 10:46:41.831 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-09-04 10:46:41.836 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-04 10:46:41.840 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 4 个匹配类型 'change_data' 的表 (尝试 1/5)
2025-09-04 10:46:41.843 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-09-04 10:46:41.848 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['异动人员表 > 2025年 > 12月 > A岗职工', '异动人员表 > 2025年 > 12月 > 离休人员工资表', '异动人员表 > 2025年 > 12月 > 退休人员工资表', '异动人员表 > 2025年 > 12月', '异动人员表 > 2025年 > 12月 > 全部在职人员工资表']
2025-09-04 10:46:41.849 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-09-04 10:46:41.850 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1396 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-09-04 10:46:41.850 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-09-04 10:46:41.854 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-04 10:46:41.855 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2180 | 找到最新工资数据路径: 工资表 > 2025年 > 05月 > unknown
2025-09-04 10:46:41.858 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1562 | 🔧 [P1-2修复] 成功获取到最新路径
2025-09-04 10:46:42.115 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-09-04 10:46:42.116 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2142 | 🚨 [架构修复] 启用模型数据同步机制
2025-09-04 10:46:42.120 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1366 | 快捷键注册完成: 18/18 个
2025-09-04 10:46:42.120 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1809 | 拖拽排序管理器初始化完成
2025-09-04 10:46:42.143 | INFO     | src.modules.data_management.data_flow_validator:__init__:85 | 🔧 [数据验证器] 初始化完成，验证级别: lenient
2025-09-04 10:46:42.144 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-09-04 10:46:42.146 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-09-04 10:46:42.146 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2195 | 🔧 [P0-紧急修复] 数据流验证器初始化成功（宽松模式）
2025-09-04 10:46:42.147 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-09-04 10:46:42.148 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-09-04 10:46:42.149 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-04 10:46:42.150 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2247 | 🎯 [统一格式管理] 使用统一格式管理器
2025-09-04 10:46:42.178 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:370 | 🔧 [新架构] 成功加载 46 个字段映射
2025-09-04 10:46:42.180 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-09-04 10:46:42.181 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2294 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-09-04 10:46:42.182 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-09-04 10:46:42.183 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-09-04 10:46:42.184 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 配置文件存在: True
2025-09-04 10:46:42.184 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 父目录存在: True
2025-09-04 10:46:42.186 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1557 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-09-04 10:46:42.188 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2301 | 列宽管理器初始化完成
2025-09-04 10:46:42.189 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2428 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-09-04 10:46:42.194 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2315 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-09-04 10:46:42.195 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-04 10:46:42.196 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-04 10:46:42.197 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-04 10:46:42.205 | INFO     | src.gui.prototype.widgets.pagination_state_manager:__init__:78 | 🔧 [P1优化] 分页状态管理器初始化完成
2025-09-04 10:46:42.206 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 0
2025-09-04 10:46:42.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2838 | 🔧 [P1优化] 表切换操作: None -> default_table
2025-09-04 10:46:42.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-04 10:46:42.213 | INFO     | src.gui.prototype.widgets.table_header_cache:__init__:67 | 🔧 [P1优化] 表头缓存管理器初始化完成，最大缓存: 100
2025-09-04 10:46:42.214 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2890 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-09-04 10:46:42.216 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-09-04 10:46:42.239 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-04 10:46:42.239 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-04 10:46:42.242 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-04 10:46:42.243 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-04 10:46:42.243 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:443 | 表格数据已设置: 0 行, 22 列
2025-09-04 10:46:42.257 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:443 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-09-04 10:46:42.294 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 60.7ms
2025-09-04 10:46:42.295 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-04 10:46:42.297 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-04 10:46:42.299 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-09-04 10:46:42.300 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-04 10:46:42.301 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-04 10:46:42.312 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-09-04 10:46:42.322 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-09-04 10:46:42.323 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-09-04 10:46:42.364 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-09-04 10:46:42.417 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5420 | 快捷键设置完成
2025-09-04 10:46:42.418 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5377 | 主窗口UI设置完成。
2025-09-04 10:46:42.419 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5614 | 🔧 [全局排序] 全局排序开关连接成功
2025-09-04 10:46:42.420 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5646 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-09-04 10:46:42.421 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5658 | ✅ 已连接分页刷新信号到主窗口
2025-09-04 10:46:42.421 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5659 | ✅ 已连接分页组件事件到新架构
2025-09-04 10:46:42.423 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5670 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-09-04 10:46:42.423 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5673 | 信号连接设置完成
2025-09-04 10:46:42.444 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6935 | 🔧 [P1-2修复] 发现 16 个表的配置
2025-09-04 10:46:42.458 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:538 |  [配置提取] 加载完整字段配置: mapping_config_A岗职工, 字段数: 21
2025-09-04 10:46:42.473 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:538 |  [配置提取] 加载完整字段配置: mapping_config_离休人员工资表, 字段数: 16
2025-09-04 10:46:42.487 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:543 |  [配置提取] 从 field_mappings 构造字段配置: salary_data_2025_05
2025-09-04 10:46:42.502 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:543 |  [配置提取] 从 field_mappings 构造字段配置: change_data_2025_12_A岗职工
2025-09-04 10:46:42.517 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:543 |  [配置提取] 从 field_mappings 构造字段配置: change_data_2025_12_全部在职人员工资表
2025-09-04 10:46:42.534 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:543 |  [配置提取] 从 field_mappings 构造字段配置: change_data_2025_12_离休人员工资表
2025-09-04 10:46:42.549 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:543 |  [配置提取] 从 field_mappings 构造字段配置: change_data_2025_12_退休人员工资表
2025-09-04 10:46:42.612 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:538 |  [配置提取] 加载完整字段配置: mapping_config_default_sheet, 字段数: 32
2025-09-04 10:46:42.629 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:543 |  [配置提取] 从 field_mappings 构造字段配置: active_employees
2025-09-04 10:46:42.642 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:543 |  [配置提取] 从 field_mappings 构造字段配置: retired_employees
2025-09-04 10:46:42.656 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:543 |  [配置提取] 从 field_mappings 构造字段配置: pension_employees
2025-09-04 10:46:42.670 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:543 |  [配置提取] 从 field_mappings 构造字段配置: part_time_employees
2025-09-04 10:46:42.688 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:543 |  [配置提取] 从 field_mappings 构造字段配置: contract_employees
2025-09-04 10:46:42.705 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:543 |  [配置提取] 从 field_mappings 构造字段配置: a_grade_employees
2025-09-04 10:46:42.722 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:538 |  [配置提取] 加载完整字段配置: mapping_config_退休人员工资表, 字段数: 27
2025-09-04 10:46:42.737 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:538 |  [配置提取] 加载完整字段配置: mapping_config_全部在职人员工资表, 字段数: 23
2025-09-04 10:46:42.739 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6945 | ✅ [P1-2修复] 已加载字段映射信息，共16个表的映射
2025-09-04 10:46:42.746 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-04 10:46:42.747 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-04 10:46:42.749 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-04 10:46:42.749 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-04 10:46:42.750 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-09-04 10:46:42.751 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_safe_set_column_count:7339 | 方案A：安全设置列数: 22
2025-09-04 10:46:42.751 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-04 10:46:42.753 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-04 10:46:42.754 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-04 10:46:42.754 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-04 10:46:42.755 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 5.2ms
2025-09-04 10:46:42.756 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-04 10:46:42.756 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-04 10:46:42.757 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-09-04 10:46:42.761 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-04 10:46:42.761 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-04 10:46:42.762 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-09-04 10:46:42.762 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8637 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-09-04 10:46:42.764 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2306 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-09-04 10:46:42.766 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2736 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-09-04 10:46:42.767 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4669 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-09-04 10:46:42.768 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5573 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-09-04 10:46:42.769 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2975 | 表格格式化完成: default_table, 类型: active_employees
2025-09-04 10:46:42.773 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-09-04 10:46:42.774 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-09-04 10:46:42.775 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-09-04 10:46:42.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:466 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-09-04 10:46:42.777 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3159 | 表格数据设置完成: 0 行, 耗时: 8.7ms
2025-09-04 10:46:42.778 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8277 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-09-04 10:46:42.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:8290 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-09-04 10:46:42.780 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1752 | 🔧 [P2-3修复] 列宽已恢复: default_table (22/22 列, 表头数量: 22)
2025-09-04 10:46:42.781 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3238 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-09-04 10:46:42.782 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2331 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-09-04 10:46:42.785 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8655 | 已显示标准空表格，表头数量: 22
2025-09-04 10:46:42.785 | INFO     | src.gui.prototype.prototype_main_window:__init__:3704 | 原型主窗口初始化完成
2025-09-04 10:46:42.940 | INFO     | __main__:main:514 | 应用程序启动成功
2025-09-04 10:46:42.947 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-04 10:46:42.948 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1474 | 执行延迟的自动选择最新数据...
2025-09-04 10:46:42.949 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2131 | 开始获取最新工资数据路径...
2025-09-04 10:46:42.951 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1333 | 🔧 [深度修复] 找到 1 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-09-04 10:46:42.954 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2180 | 找到最新工资数据路径: 工资表 > 2025年 > 05月 > unknown
2025-09-04 10:46:42.954 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1562 | 🔧 [P1-2修复] 成功获取到最新路径
2025-09-04 10:46:42.957 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-04 10:46:42.958 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:8312 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-09-04 10:46:43.018 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-09-04 10:46:43.018 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2079 | MainWorkspaceArea 响应式适配: sm
2025-09-04 10:46:43.518 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9539 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-09-04 10:46:43.519 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9449 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-09-04 10:46:43.522 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9463 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-09-04 10:46:43.522 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9997 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-09-04 10:46:43.532 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9469 | 🔧 [P0-1] 智能显示亮度修复完成
2025-09-04 10:46:48.661 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-09-04 10:46:48.661 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8460 | 检测到当前在工资表TAB，生成工资表默认路径
2025-09-04 10:46:48.661 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:5906 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 09月 > 全部在职人员。打开导入对话框。
2025-09-04 10:46:48 - src.gui.unified_data_import_window - INFO - 初始化统一数据导入窗口
2025-09-04 10:46:48.849 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-04 10:46:48.849 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-04 10:46:48.849 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-04 10:46:48.849 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-04 10:46:48.849 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-09-04 10:46:48.864 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-09-04 10:46:48.864 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-09-04 10:46:48.864 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-04 10:46:48.864 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-09-04 10:46:48.864 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-09-04 10:46:48.864 | INFO     | src.core.config_cache_manager:__init__:67 | 配置缓存管理器初始化完成: cache\config_cache, 最大条目数: 100
2025-09-04 10:46:48.864 | INFO     | src.modules.data_import.change_data_config_manager:__init__:69 | 配置缓存已启用
2025-09-04 10:46:48.864 | INFO     | src.modules.data_import.change_data_config_manager:__init__:75 | 配置管理器初始化完成，配置目录: C:\test\salary_changes\salary_changes\state\change_data_configs
2025-09-04 10:46:48.864 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:78 | 多Sheet导入器初始化完成
2025-09-04 10:46:48 - src.gui.core.smart_mapping_engine - INFO - 成功加载历史配置: 3 个配置
2025-09-04 10:46:48 - src.gui.core.smart_mapping_engine - INFO - 智能映射引擎初始化完成
2025-09-04 10:46:48 - src.gui.core.template_manager - INFO - 模板管理器初始化完成: 内置模板 3 个, 用户模板 0 个
2025-09-04 10:46:48 - src.gui.core.validation_engine - INFO - 验证引擎初始化完成
2025-09-04 10:46:48.945 | INFO     | src.modules.data_import.sheet_config_manager:__init__:128 | Sheet配置管理器初始化完成，配置目录: state\sheet_configs
2025-09-04 10:46:48 - src.gui.unified_data_import_window - INFO - 统一导入管理器初始化完成（包含第二阶段功能）
2025-09-04 10:46:48.945 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-09-04 10:46:48 - src.gui.unified_data_import_window - INFO - 核心组件初始化成功
2025-09-04 10:46:48 - src.gui.unified_data_import_window - INFO - 接收到高级配置变化: {'file_import': {'default_import_path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'supported_formats': ['xlsx', 'xls', 'csv'], 'max_file_size_mb': 100, 'auto_detect_encoding': True, 'sheet_selection_strategy': 'all', 'data_start_row': 2, 'header_row': 1, 'skip_rows': 0, 'skip_footer_rows': 0, 'auto_detect_header': True, 'ignore_empty_rows': True}, 'field_mapping': {'mapping_algorithm': 'fuzzy_match', 'similarity_threshold': 80, 'auto_mapping_enabled': True, 'required_field_check': True, 'field_type_validation': True, 'save_mapping_history': True}, 'smart_recommendations': {'confidence_threshold': 70, 'enable_history_learning': True, 'enable_semantic_analysis': True, 'auto_apply_high_confidence': False, 'template_priority': 0, 'max_saved_templates': 50}, 'data_processing': {'strict_validation': False, 'null_value_strategy': 0, 'auto_type_conversion': True, 'duplicate_strategy': 0, 'batch_size': 1000, 'error_tolerance': 10, 'remove_duplicates': False, 'handle_missing_values': 'keep', 'format_numbers': True, 'format_dates': True, 'trim_whitespace': True, 'convert_data_types': True, 'data_validation': True, 'custom_rules': [], 'saved_templates': []}, 'ui_customization': {'table_row_limit': 200, 'show_detailed_logs': False, 'show_confidence_indicators': True, 'auto_save_interval': 5, 'show_confirmation_dialogs': True, 'show_shortcuts': True}, 'performance': {'max_memory_usage': 2048, 'enable_caching': True, 'preload_data': False, 'thread_count': 4, 'enable_async_processing': True, 'progress_update_frequency': 100}}
2025-09-04 10:46:48 - src.gui.unified_data_import_window - INFO - 应用默认导入文件: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 10:46:48 - src.gui.unified_data_import_window - INFO - UI未初始化，保存默认路径供后续应用: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 10:46:48 - src.gui.unified_data_import_window - INFO - 文件导入配置已应用，包括Excel表结构设置: {'data_start_row': 2, 'header_row': 1, 'skip_rows': 0, 'skip_footer_rows': 0, 'auto_detect_header': True, 'ignore_empty_rows': True}
2025-09-04 10:46:48 - src.gui.unified_data_import_window - INFO - 字段映射配置已应用
2025-09-04 10:46:48 - src.gui.unified_data_import_window - INFO - 智能推荐配置已应用
2025-09-04 10:46:48 - src.gui.unified_data_import_window - INFO - 数据处理配置已应用
2025-09-04 10:46:48 - src.gui.unified_data_import_window - INFO - 界面个性化配置已应用
2025-09-04 10:46:48 - src.gui.unified_data_import_window - INFO - 应用性能优化配置完成
2025-09-04 10:46:48 - src.gui.unified_data_import_window - INFO - 所有高级配置域已成功应用: ['file_import', 'field_mapping', 'smart_recommendations', 'data_processing', 'ui_customization', 'performance']
2025-09-04 10:46:48 - src.gui.unified_data_import_window - INFO - 🔧 [P0修复] 已加载保存的高级配置
2025-09-04 10:46:48 - src.gui.unified_data_import_window - INFO - 工作线程初始化完成
2025-09-04 10:46:48.958 | INFO     | src.modules.data_import.sheet_config_manager:__init__:128 | Sheet配置管理器初始化完成，配置目录: state\sheet_configs
2025-09-04 10:46:48 - src.gui.core.smart_mapping_engine - INFO - 成功加载历史配置: 3 个配置
2025-09-04 10:46:48 - src.gui.core.smart_mapping_engine - INFO - 智能映射引擎初始化完成
2025-09-04 10:46:48 - src.gui.core.template_manager - INFO - 模板管理器初始化完成: 内置模板 3 个, 用户模板 0 个
2025-09-04 10:46:48 - src.gui.core.validation_engine - INFO - 验证引擎初始化完成
2025-09-04 10:46:48 - src.gui.performance_optimizer - INFO - 性能优化器初始化完成
2025-09-04 10:46:48.990 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-09-04 10:46:48 - src.gui.unified_data_import_window - INFO - 💾 [P1-4修复] 开始初始化ConfigSyncManager...
2025-09-04 10:46:48.990 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-09-04 10:46:48.990 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-09-04 10:46:49.005 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:154 | 使用已存在的配置文件: state\data\field_mappings.json
2025-09-04 10:46:49.005 | INFO     | src.modules.data_import.config_sync_manager:__init__:82 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-09-04 10:46:49.021 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-09-04 10:46:49.021 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-09-04 10:46:49.021 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-09-04 10:46:49.021 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-09-04 10:46:49.021 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-04 10:46:49.021 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-04 10:46:49.021 | INFO     | src.core.unified_data_request_manager:__init__:217 | 统一数据请求管理器初始化完成
2025-09-04 10:46:49.021 | INFO     | src.core.unified_state_manager:_load_state:585 | 状态已从文件加载: state/unified_state.json
2025-09-04 10:46:49.021 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-09-04 10:46:49.021 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-09-04 10:46:49.021 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 30.9ms
2025-09-04 10:46:49 - src.gui.unified_data_import_window - INFO - 💾 [P1-1修复] ✅ 架构工厂初始化成功
2025-09-04 10:46:49 - src.gui.unified_data_import_window - INFO - 💾 [P1-1修复] ✅ 通过架构工厂获取ConfigSyncManager成功
2025-09-04 10:46:49.115 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-09-04 10:46:49.115 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:514 | 注册规则类型: number
2025-09-04 10:46:49.115 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:514 | 注册规则类型: string
2025-09-04 10:46:49.115 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:514 | 注册规则类型: date
2025-09-04 10:46:49.115 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:514 | 注册规则类型: code
2025-09-04 10:46:49.115 | INFO     | src.modules.data_import.formatting_engine:register_rule_type:514 | 注册规则类型: custom
2025-09-04 10:46:49.115 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: salary_float - 工资金额
2025-09-04 10:46:49.115 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: employee_id_string - 工号
2025-09-04 10:46:49.115 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: name_string - 姓名
2025-09-04 10:46:49.115 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: date_string - 日期
2025-09-04 10:46:49.115 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: id_number_string - 身份证号
2025-09-04 10:46:49.115 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: code_string - 代码
2025-09-04 10:46:49.115 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: float - 浮点数
2025-09-04 10:46:49.115 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: integer - 整数
2025-09-04 10:46:49.130 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: text_string - 文本字符串
2025-09-04 10:46:49.130 | INFO     | src.modules.data_import.formatting_engine:_register_builtin_field_type:525 | 🔧 [方案一实施] 注册内置字段类型: personnel_category_code - 人员类别代码
2025-09-04 10:46:49.130 | INFO     | src.modules.data_import.field_type_manager:__init__:41 | 字段类型管理器初始化完成，存储目录: C:\test\salary_changes\salary_changes\state\field_types
2025-09-04 10:46:49.130 | INFO     | src.modules.data_import.formatting_engine:reload_custom_field_types:825 | 🔧 [方案一实施] 重新加载了 0 个自定义字段类型
2025-09-04 10:46:49 - src.gui.widgets.field_type_config_widget - INFO - 已加载 10 个内置类型和 0 个自定义类型
2025-09-04 10:46:49 - src.gui.widgets.field_type_config_widget - INFO - 字段类型配置组件初始化完成
2025-09-04 10:46:49 - src.gui.unified_data_import_window - INFO - 响应式分割器设置完成: 左侧=308px, 右侧=1092px
2025-09-04 10:46:49 - src.gui.unified_data_import_window - INFO - UI界面创建完成
2025-09-04 10:46:49 - src.gui.unified_data_import_window - INFO - 应用待处理的默认文件: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 10:46:49.406 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-09-04 10:46:49.407 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 10:46:49.409 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 10:46:49.410 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 10:46:49.511 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-04 10:46:49.513 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 10:46:49.516 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-04 10:46:49.517 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-09-04 10:46:49.520 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-04 10:46:49.520 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-04 10:46:49.525 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-09-04 10:46:49.527 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 10:46:49.631 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 10:46:49.632 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-09-04 10:46:49.635 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 10:46:49.735 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 10:46:49.738 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-09-04 10:46:49.742 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 10:46:49.838 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 10:46:49.840 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-04 10:46:49 - src.gui.unified_data_import_window - INFO - 已加载 4 个Sheet
2025-09-04 10:46:49 - src.gui.unified_data_import_window - INFO - 成功加载 4 个工作表
2025-09-04 10:46:49 - src.gui.unified_data_import_window - INFO - 待处理配置应用完成: C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 10:46:49 - src.gui.unified_data_import_window - INFO - 信号连接完成
2025-09-04 10:46:49 - src.gui.unified_data_import_window - INFO - 统一数据导入窗口初始化完成
2025-09-04 10:46:49 - src.gui.unified_data_import_window - INFO - 响应式分割器设置完成: 左侧=308px, 右侧=1092px
2025-09-04 10:46:49 - src.gui.unified_data_import_window - INFO - 响应式分割器设置完成: 左侧=308px, 右侧=1092px
2025-09-04 10:46:49 - src.gui.unified_data_import_window - INFO - 响应式布局初始化完成
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 选中Sheet: A岗职工
2025-09-04 10:46:51.557 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet 'A岗职工' 创建默认配置
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 当前Sheet变化: A岗职工
2025-09-04 10:46:51 - src.gui.unified_data_import_window - WARNING - 🔧 [方案B] mapping_tab没有_smart_save_user_modified_configs方法
2025-09-04 10:46:51 - src.gui.unified_data_import_window - WARNING - 🔧 [方案B] 智能保存失败，尝试强制保存
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🚨 [强制保存] 开始批量保存所有字段的当前配置状态
2025-09-04 10:46:51 - src.gui.unified_data_import_window - WARNING - 🚨 [强制保存] ConfigSyncManager未初始化或无字段配置需要保存
2025-09-04 10:46:51 - src.gui.unified_data_import_window - WARNING - 🔧 [方案B] 强制保存当前Sheet配置失败
2025-09-04 10:46:51 - src.gui.unified_data_import_window - WARNING - 🔧 [方案B] 保存当前Sheet的字段映射配置失败
2025-09-04 10:46:51 - src.gui.unified_data_import_window - WARNING - ⚠️ 🔧 [方案B] Sheet切换前配置保存失败，可能导致配置丢失
2025-09-04 10:46:51.572 | INFO     | src.modules.data_import.formatting_engine:clear_temporary_field_types:866 | 🔧 [方案一实施] 已清理 0 个临时字段类型
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 已清理临时字段类型
2025-09-04 10:46:51.572 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet 'A岗职工' 创建默认配置
2025-09-04 10:46:51.588 | INFO     | src.modules.data_import.config_template_manager:_load_templates:579 | 加载了 10 个模板
2025-09-04 10:46:51.588 | INFO     | src.modules.data_import.config_template_manager:_init_builtin_templates:184 | 初始化了 4 个内置模板
2025-09-04 10:46:51.588 | INFO     | src.modules.data_import.config_template_manager:__init__:100 | 配置模板管理器初始化完成，模板目录: state\config_templates
2025-09-04 10:46:51.588 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-09-04 10:46:51.588 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: A岗职工
2025-09-04 10:46:51.588 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 10:46:51.588 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 10:46:51.588 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 10:46:51.697 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-09-04 10:46:51.697 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 10:46:51.697 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-04 10:46:51.697 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 21列
2025-09-04 10:46:51.697 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 21列
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 为Sheet 'A岗职工' 获取到 21 个字段
2025-09-04 10:46:51 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 加载Sheet 'A岗职工' 配置，表名: mapping_config_default_sheet
2025-09-04 10:46:51.729 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:538 |  [配置提取] 加载完整字段配置: mapping_config_default_sheet, 字段数: 32
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 找到 32 个已保存的字段配置
2025-09-04 10:46:51 - src.gui.unified_data_import_window - ERROR - 🔧 [方案B] 加载Sheet配置失败: 'UnifiedMappingConfigWidget' object has no attribute '_enhance_configs_with_version_control'
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 加载Excel字段: 21 个字段, 表类型: 💰 工资表
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '序号' 使用智能推断类型: text_string
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '序号' (第0行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '序号' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '工号' 使用智能推断类型: employee_id_string
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '工号' (第1行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '工号' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '姓名' 使用智能推断类型: name_string
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '姓名' (第2行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '姓名' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '部门名称' 使用智能推断类型: text_string
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '部门名称' (第3行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '部门名称' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '人员类别' 使用智能推断类型: personnel_category_code
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '人员类别' (第4行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '人员类别' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '人员类别代码' 使用智能推断类型: personnel_category_code
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '人员类别代码' (第5行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '人员类别代码' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2025年岗位工资' 使用智能推断类型: salary_float
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2025年岗位工资' (第6行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2025年岗位工资' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2025年校龄工资' 使用智能推断类型: salary_float
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2025年校龄工资' (第7行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2025年校龄工资' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '津贴' 使用智能推断类型: salary_float
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '津贴' (第8行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '津贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '结余津贴' 使用智能推断类型: salary_float
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '结余津贴' (第9行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '结余津贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2025年基础性绩效' 使用智能推断类型: salary_float
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2025年基础性绩效' (第10行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2025年基础性绩效' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '卫生费' 使用智能推断类型: salary_float
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '卫生费' (第11行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '卫生费' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2025年生活补贴' 使用智能推断类型: salary_float
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2025年生活补贴' (第12行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2025年生活补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '车补' 使用智能推断类型: text_string
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '车补' (第13行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '车补' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2025年奖励性绩效预发' 使用智能推断类型: salary_float
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2025年奖励性绩效预发' (第14行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2025年奖励性绩效预发' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '补发' 使用智能推断类型: text_string
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '补发' (第15行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '补发' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '借支' 使用智能推断类型: text_string
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '借支' (第16行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '借支' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '应发工资' 使用智能推断类型: salary_float
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '应发工资' (第17行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '应发工资' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2025公积金' 使用智能推断类型: text_string
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2025公积金' (第18行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2025公积金' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '保险扣款' 使用智能推断类型: text_string
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '保险扣款' (第19行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '保险扣款' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '代扣代存养老保险' 使用智能推断类型: text_string
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '代扣代存养老保险' (第20行) 设置即时保存机制
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '代扣代存养老保险' currentTextChanged -> 即时保存处理函数
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:46:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段映射表格创建完成: 21 行
2025-09-04 10:46:51 - src.gui.core.smart_mapping_engine - INFO - 开始生成智能映射: 类型=💰 工资表, 字段数=21
2025-09-04 10:46:51 - src.gui.core.smart_mapping_engine - INFO - 智能映射生成完成: 高置信度 5 个, 中等置信度 3 个
2025-09-04 10:46:53 - src.gui.unified_data_import_window - INFO - 智能映射生成成功
2025-09-04 10:46:53 - src.gui.unified_data_import_window - INFO - 🔧 [修复] Sheet 'A岗职工' 字段映射已更新
2025-09-04 10:46:53 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: A岗职工
2025-09-04 10:46:53.941 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 10:46:53.942 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 10:46:53.944 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 10:46:54.053 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-09-04 10:46:54.055 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 10:46:54.057 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-04 10:46:54.057 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 63行 x 21列
2025-09-04 10:46:54.059 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-04 10:46:54.061 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-09-04 10:46:54.065 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 62行 × 21列
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 开始获取字段映射配置，表格行数: 21
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第0行字段: '序号'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '序号' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '序号', 'db_field': '序号'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第1行字段: '工号'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工号', 数据: 'employee_id_string'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '工号' 最终配置: {'field_type': 'employee_id_string', 'data_type': 'VARCHAR(100)', 'is_required': True, 'display_name': '工号', 'db_field': '工号'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第2行字段: '姓名'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '姓名', 数据: 'name_string'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '姓名' 最终配置: {'field_type': 'name_string', 'data_type': 'VARCHAR(100)', 'is_required': True, 'display_name': '姓名', 'db_field': '姓名'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第3行字段: '部门名称'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '部门名称' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '部门名称', 'db_field': '部门'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第4行字段: '人员类别'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '人员类别代码', 数据: 'personnel_category_code'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '人员类别' 最终配置: {'field_type': 'personnel_category_code', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '人员类别', 'db_field': '人员类别'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第5行字段: '人员类别代码'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '人员类别代码', 数据: 'personnel_category_code'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '人员类别代码' 最终配置: {'field_type': 'personnel_category_code', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '人员类别代码', 'db_field': '人员类别代码'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第6行字段: '2025年岗位工资'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年岗位工资' 最终配置: {'field_type': 'salary_float', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '2025年岗位工资', 'db_field': 'position'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第7行字段: '2025年校龄工资'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年校龄工资' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年校龄工资', 'db_field': 'money'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第8行字段: '津贴'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '津贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '津贴', 'db_field': '津贴'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第9行字段: '结余津贴'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '结余津贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '结余津贴', 'db_field': 'money'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第10行字段: '2025年基础性绩效'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年基础性绩效' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年基础性绩效', 'db_field': 'money'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第11行字段: '卫生费'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '卫生费' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '卫生费', 'db_field': '卫生费'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第12行字段: '2025年生活补贴'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年生活补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年生活补贴', 'db_field': 'money'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第13行字段: '车补'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '车补' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '车补', 'db_field': '车补'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第14行字段: '2025年奖励性绩效预发'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年奖励性绩效预发' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年奖励性绩效预发', 'db_field': 'money'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第15行字段: '补发'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '补发' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '补发', 'db_field': '补发'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第16行字段: '借支'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '借支' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '借支', 'db_field': '借支'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第17行字段: '应发工资'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '应发工资' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': True, 'display_name': '应发工资', 'db_field': 'money'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第18行字段: '2025公积金'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025公积金' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025公积金', 'db_field': '2025公积金'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第19行字段: '保险扣款'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '保险扣款' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '保险扣款', 'db_field': '扣款'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第20行字段: '代扣代存养老保险'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '代扣代存养老保险' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '代扣代存养老保险', 'db_field': 'money'}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 获取字段映射配置完成，共 21 个字段
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [关键修复] 获取到 21 个字段映射配置
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 开始显示预览数据: A岗职工, 数据行数: 62
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段映射配置: {'序号': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '工号': {'field_type': 'employee_id_string', 'data_type': 'VARCHAR(100)', 'is_required': True}, '姓名': {'field_type': 'name_string', 'data_type': 'VARCHAR(100)', 'is_required': True}, '部门名称': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '人员类别': {'field_type': 'personnel_category_code', 'data_type': 'VARCHAR(100)', 'is_required': False}, '人员类别代码': {'field_type': 'personnel_category_code', 'data_type': 'VARCHAR(100)', 'is_required': False}, '2025年岗位工资': {'field_type': 'salary_float', 'data_type': 'VARCHAR(100)', 'is_required': False}, '2025年校龄工资': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '津贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '结余津贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '2025年基础性绩效': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '卫生费': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '2025年生活补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '车补': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '2025年奖励性绩效预发': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '补发': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '借支': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '应发工资': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': True}, '2025公积金': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '保险扣款': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '代扣代存养老保险': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}}
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 获取格式化引擎成功
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 设置表格结构: 21 列, 62 行
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '序号' 映射到类型: text_string
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '工号' 映射到类型: employee_id_string
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '工号' 格式化: '34660024.0' -> '34660024'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '姓名' 映射到类型: name_string
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '部门名称' 映射到类型: text_string
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员类别' 映射到类型: personnel_category_code
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员类别代码' 映射到类型: personnel_category_code
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员类别代码' 格式化: '1.0' -> '01'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年岗位工资' 映射到类型: salary_float
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年岗位工资' 格式化: '1720.0' -> '1,720.00'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年校龄工资' 映射到类型: salary_float
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年校龄工资' 格式化: '1061.0' -> '1,061.00'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '津贴' 映射到类型: salary_float
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '津贴' 格式化: '0.0' -> '0.00'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '结余津贴' 映射到类型: salary_float
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '结余津贴' 格式化: '56.0' -> '56.00'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年基础性绩效' 映射到类型: salary_float
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年基础性绩效' 格式化: '2696.0' -> '2,696.00'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '卫生费' 映射到类型: salary_float
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '卫生费' 格式化: '20.0' -> '20.00'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年生活补贴' 映射到类型: salary_float
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年生活补贴' 格式化: '493.0' -> '493.00'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '车补' 映射到类型: text_string
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年奖励性绩效预发' 映射到类型: salary_float
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年奖励性绩效预发' 格式化: '1500.0' -> '1,500.00'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '补发' 映射到类型: text_string
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '借支' 映射到类型: text_string
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '应发工资' 映射到类型: salary_float
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '应发工资' 格式化: '7546.0' -> '7,546.00'
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025公积金' 映射到类型: text_string
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '保险扣款' 映射到类型: text_string
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '代扣代存养老保险' 映射到类型: text_string
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: A岗职工 - 62 行
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 62 行, 21 列
2025-09-04 10:46:54 - src.gui.unified_data_import_window - INFO - 已加载Sheet 'A岗职工' 的预览数据: 62 行
2025-09-04 10:47:06 - src.gui.unified_data_import_window - ERROR - 🔧 [方案B] 字段类型变化处理失败: 'UnifiedMappingConfigWidget' object has no attribute '_save_field_type_immediately_with_version_control'
2025-09-04 10:47:06 - src.gui.unified_data_import_window - INFO - 字段类型已回滚: 序号 -> integer
2025-09-04 10:47:07 - src.gui.unified_data_import_window - INFO - 💾 [防抖保存] *** 执行延迟保存 *** 字段: '序号', 类型: integer
2025-09-04 10:47:07 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:47:07 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:47:07.172 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1054 | 🔧 [方案B] 用户修改配置已保存: mapping_config_default_sheet.序号 (版本: v1756954027)
2025-09-04 10:47:07 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 字段配置保存成功: mapping_config_default_sheet.序号
2025-09-04 10:47:07 - src.gui.unified_data_import_window - INFO - 💾 [防抖保存] 保存结果: 成功
2025-09-04 10:47:07.175 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 序号
2025-09-04 10:47:10 - src.gui.unified_data_import_window - ERROR - 🔧 [方案B] 字段类型变化处理失败: 'UnifiedMappingConfigWidget' object has no attribute '_save_field_type_immediately_with_version_control'
2025-09-04 10:47:10 - src.gui.unified_data_import_window - INFO - 字段类型已回滚: 人员类别 -> text_string
2025-09-04 10:47:11 - src.gui.unified_data_import_window - INFO - 💾 [防抖保存] *** 执行延迟保存 *** 字段: '人员类别', 类型: text_string
2025-09-04 10:47:11 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:47:11 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:47:11.201 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1054 | 🔧 [方案B] 用户修改配置已保存: mapping_config_default_sheet.人员类别 (版本: v1756954031)
2025-09-04 10:47:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 字段配置保存成功: mapping_config_default_sheet.人员类别
2025-09-04 10:47:11 - src.gui.unified_data_import_window - INFO - 💾 [防抖保存] 保存结果: 成功
2025-09-04 10:47:11.204 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员类别
2025-09-04 10:47:14 - src.gui.unified_data_import_window - ERROR - 🔧 [方案B] 字段类型变化处理失败: 'UnifiedMappingConfigWidget' object has no attribute '_save_field_type_immediately_with_version_control'
2025-09-04 10:47:14 - src.gui.unified_data_import_window - INFO - 字段类型已回滚: 车补 -> salary_float
2025-09-04 10:47:15 - src.gui.unified_data_import_window - INFO - 💾 [防抖保存] *** 执行延迟保存 *** 字段: '车补', 类型: salary_float
2025-09-04 10:47:15 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:47:15 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:47:15.359 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1054 | 🔧 [方案B] 用户修改配置已保存: mapping_config_default_sheet.车补 (版本: v1756954035)
2025-09-04 10:47:15 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 字段配置保存成功: mapping_config_default_sheet.车补
2025-09-04 10:47:15 - src.gui.unified_data_import_window - INFO - 💾 [防抖保存] 保存结果: 成功
2025-09-04 10:47:15.359 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 车补
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔄 [修复] 选项卡切换到: 👁️ 预览验证 (索引: 2)
2025-09-04 10:47:16 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:47:16.328 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:448 | 字段映射保存成功: mapping_config_default_sheet
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_default_sheet
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔄 [修复] 切换到预览选项卡，开始刷新格式化
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 开始获取字段映射配置，表格行数: 21
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第0行字段: '序号'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '整数', 数据: 'integer'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '序号' 最终配置: {'field_type': 'integer', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '序号', 'db_field': '序号'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第1行字段: '工号'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工号', 数据: 'employee_id_string'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '工号' 最终配置: {'field_type': 'employee_id_string', 'data_type': 'VARCHAR(100)', 'is_required': True, 'display_name': '工号', 'db_field': '工号'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第2行字段: '姓名'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '姓名', 数据: 'name_string'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '姓名' 最终配置: {'field_type': 'name_string', 'data_type': 'VARCHAR(100)', 'is_required': True, 'display_name': '姓名', 'db_field': '姓名'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第3行字段: '部门名称'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '部门名称' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '部门名称', 'db_field': '部门'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第4行字段: '人员类别'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '人员类别' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '人员类别', 'db_field': '人员类别'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第5行字段: '人员类别代码'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '人员类别代码', 数据: 'personnel_category_code'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '人员类别代码' 最终配置: {'field_type': 'personnel_category_code', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '人员类别代码', 'db_field': '人员类别代码'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第6行字段: '2025年岗位工资'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年岗位工资' 最终配置: {'field_type': 'salary_float', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '2025年岗位工资', 'db_field': 'position'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第7行字段: '2025年校龄工资'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年校龄工资' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年校龄工资', 'db_field': 'money'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第8行字段: '津贴'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '津贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '津贴', 'db_field': '津贴'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第9行字段: '结余津贴'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '结余津贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '结余津贴', 'db_field': 'money'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第10行字段: '2025年基础性绩效'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年基础性绩效' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年基础性绩效', 'db_field': 'money'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第11行字段: '卫生费'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '卫生费' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '卫生费', 'db_field': '卫生费'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第12行字段: '2025年生活补贴'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年生活补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年生活补贴', 'db_field': 'money'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第13行字段: '车补'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '车补' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '车补', 'db_field': '车补'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第14行字段: '2025年奖励性绩效预发'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年奖励性绩效预发' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年奖励性绩效预发', 'db_field': 'money'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第15行字段: '补发'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '补发' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '补发', 'db_field': '补发'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第16行字段: '借支'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '借支' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '借支', 'db_field': '借支'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第17行字段: '应发工资'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '应发工资' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': True, 'display_name': '应发工资', 'db_field': 'money'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第18行字段: '2025公积金'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025公积金' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025公积金', 'db_field': '2025公积金'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第19行字段: '保险扣款'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '保险扣款' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '保险扣款', 'db_field': '扣款'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第20行字段: '代扣代存养老保险'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '代扣代存养老保险' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '代扣代存养老保险', 'db_field': 'money'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 获取字段映射配置完成，共 21 个字段
2025-09-04 10:47:16.380 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 序号
2025-09-04 10:47:16.380 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 工号
2025-09-04 10:47:16.380 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 姓名
2025-09-04 10:47:16.380 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 部门名称
2025-09-04 10:47:16.380 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员类别
2025-09-04 10:47:16.380 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员类别代码
2025-09-04 10:47:16.380 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年岗位工资
2025-09-04 10:47:16.380 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年校龄工资
2025-09-04 10:47:16.380 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 津贴
2025-09-04 10:47:16.380 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 结余津贴
2025-09-04 10:47:16.380 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年基础性绩效
2025-09-04 10:47:16.391 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 卫生费
2025-09-04 10:47:16.391 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年生活补贴
2025-09-04 10:47:16.391 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 车补
2025-09-04 10:47:16.391 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年奖励性绩效预发
2025-09-04 10:47:16.391 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 补发
2025-09-04 10:47:16.391 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 借支
2025-09-04 10:47:16.391 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 应发工资
2025-09-04 10:47:16.391 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025公积金
2025-09-04 10:47:16.391 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 保险扣款
2025-09-04 10:47:16.391 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 代扣代存养老保险
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔄 [修复] 使用缓存的预览数据
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 开始获取字段映射配置，表格行数: 21
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第0行字段: '序号'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '整数', 数据: 'integer'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '序号' 最终配置: {'field_type': 'integer', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '序号', 'db_field': '序号'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第1行字段: '工号'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工号', 数据: 'employee_id_string'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '工号' 最终配置: {'field_type': 'employee_id_string', 'data_type': 'VARCHAR(100)', 'is_required': True, 'display_name': '工号', 'db_field': '工号'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第2行字段: '姓名'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '姓名', 数据: 'name_string'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '姓名' 最终配置: {'field_type': 'name_string', 'data_type': 'VARCHAR(100)', 'is_required': True, 'display_name': '姓名', 'db_field': '姓名'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第3行字段: '部门名称'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '部门名称' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '部门名称', 'db_field': '部门'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第4行字段: '人员类别'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '人员类别' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '人员类别', 'db_field': '人员类别'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第5行字段: '人员类别代码'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '人员类别代码', 数据: 'personnel_category_code'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '人员类别代码' 最终配置: {'field_type': 'personnel_category_code', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '人员类别代码', 'db_field': '人员类别代码'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第6行字段: '2025年岗位工资'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年岗位工资' 最终配置: {'field_type': 'salary_float', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '2025年岗位工资', 'db_field': 'position'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第7行字段: '2025年校龄工资'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年校龄工资' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年校龄工资', 'db_field': 'money'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第8行字段: '津贴'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '津贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '津贴', 'db_field': '津贴'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第9行字段: '结余津贴'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '结余津贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '结余津贴', 'db_field': 'money'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第10行字段: '2025年基础性绩效'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年基础性绩效' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年基础性绩效', 'db_field': 'money'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第11行字段: '卫生费'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '卫生费' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '卫生费', 'db_field': '卫生费'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第12行字段: '2025年生活补贴'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年生活补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年生活补贴', 'db_field': 'money'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第13行字段: '车补'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '车补' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '车补', 'db_field': '车补'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第14行字段: '2025年奖励性绩效预发'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年奖励性绩效预发' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年奖励性绩效预发', 'db_field': 'money'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第15行字段: '补发'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '补发' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '补发', 'db_field': '补发'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第16行字段: '借支'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '借支' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '借支', 'db_field': '借支'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第17行字段: '应发工资'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '应发工资' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': True, 'display_name': '应发工资', 'db_field': 'money'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第18行字段: '2025公积金'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025公积金' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025公积金', 'db_field': '2025公积金'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第19行字段: '保险扣款'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '保险扣款' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '保险扣款', 'db_field': '扣款'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第20行字段: '代扣代存养老保险'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '代扣代存养老保险' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '代扣代存养老保险', 'db_field': 'money'}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 获取字段映射配置完成，共 21 个字段
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔄 [修复] 获取到字段映射配置: 21 个字段
2025-09-04 10:47:16.437 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 序号
2025-09-04 10:47:16.437 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 工号
2025-09-04 10:47:16.437 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 姓名
2025-09-04 10:47:16.437 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 部门名称
2025-09-04 10:47:16.453 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员类别
2025-09-04 10:47:16.453 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员类别代码
2025-09-04 10:47:16.453 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年岗位工资
2025-09-04 10:47:16.453 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年校龄工资
2025-09-04 10:47:16.453 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 津贴
2025-09-04 10:47:16.453 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 结余津贴
2025-09-04 10:47:16.453 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年基础性绩效
2025-09-04 10:47:16.453 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 卫生费
2025-09-04 10:47:16.453 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年生活补贴
2025-09-04 10:47:16.453 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 车补
2025-09-04 10:47:16.453 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025年奖励性绩效预发
2025-09-04 10:47:16.453 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 补发
2025-09-04 10:47:16.453 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 借支
2025-09-04 10:47:16.453 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 应发工资
2025-09-04 10:47:16.453 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 2025公积金
2025-09-04 10:47:16.453 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 保险扣款
2025-09-04 10:47:16.453 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 代扣代存养老保险
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔄 [修复] 已注册 21 个字段类型到格式化引擎
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 开始显示预览数据: A岗职工, 数据行数: 62
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段映射配置: {'序号': {'field_type': 'integer', 'data_type': 'VARCHAR(100)', 'is_required': False}, '工号': {'field_type': 'employee_id_string', 'data_type': 'VARCHAR(100)', 'is_required': True}, '姓名': {'field_type': 'name_string', 'data_type': 'VARCHAR(100)', 'is_required': True}, '部门名称': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '人员类别': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '人员类别代码': {'field_type': 'personnel_category_code', 'data_type': 'VARCHAR(100)', 'is_required': False}, '2025年岗位工资': {'field_type': 'salary_float', 'data_type': 'VARCHAR(100)', 'is_required': False}, '2025年校龄工资': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '津贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '结余津贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '2025年基础性绩效': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '卫生费': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '2025年生活补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '车补': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '2025年奖励性绩效预发': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '补发': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '借支': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '应发工资': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': True}, '2025公积金': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '保险扣款': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '代扣代存养老保险': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}}
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 获取格式化引擎成功
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 设置表格结构: 21 列, 62 行
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '序号' 映射到类型: integer
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '序号' 格式化: '1.0' -> '1'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '工号' 映射到类型: employee_id_string
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '工号' 格式化: '34660024.0' -> '34660024'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '姓名' 映射到类型: name_string
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '部门名称' 映射到类型: text_string
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员类别' 映射到类型: text_string
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员类别代码' 映射到类型: personnel_category_code
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员类别代码' 格式化: '1.0' -> '01'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年岗位工资' 映射到类型: salary_float
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年岗位工资' 格式化: '1720.0' -> '1,720.00'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年校龄工资' 映射到类型: salary_float
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年校龄工资' 格式化: '1061.0' -> '1,061.00'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '津贴' 映射到类型: salary_float
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '津贴' 格式化: '0.0' -> '0.00'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '结余津贴' 映射到类型: salary_float
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '结余津贴' 格式化: '56.0' -> '56.00'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年基础性绩效' 映射到类型: salary_float
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年基础性绩效' 格式化: '2696.0' -> '2,696.00'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '卫生费' 映射到类型: salary_float
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '卫生费' 格式化: '20.0' -> '20.00'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年生活补贴' 映射到类型: salary_float
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年生活补贴' 格式化: '493.0' -> '493.00'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '车补' 映射到类型: salary_float
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年奖励性绩效预发' 映射到类型: salary_float
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年奖励性绩效预发' 格式化: '1500.0' -> '1,500.00'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '补发' 映射到类型: text_string
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '借支' 映射到类型: text_string
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '应发工资' 映射到类型: salary_float
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '应发工资' 格式化: '7546.0' -> '7,546.00'
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025公积金' 映射到类型: text_string
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '保险扣款' 映射到类型: text_string
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '代扣代存养老保险' 映射到类型: text_string
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: A岗职工 - 62 行
2025-09-04 10:47:16 - src.gui.unified_data_import_window - INFO - 🔄 [修复] 已刷新格式化 - 21 个字段映射
2025-09-04 10:47:30 - src.gui.unified_data_import_window - INFO - 🔄 [修复] 选项卡切换到: 🔗 字段映射 (索引: 0)
2025-09-04 10:47:30 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:47:30 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:47:31.070 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:448 | 字段映射保存成功: mapping_config_default_sheet
2025-09-04 10:47:31 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_default_sheet
2025-09-04 10:47:33 - src.gui.unified_data_import_window - INFO - 选中Sheet: 离休人员工资表
2025-09-04 10:47:33.085 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '离休人员工资表' 创建默认配置
2025-09-04 10:47:33 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 当前Sheet变化: 离休人员工资表
2025-09-04 10:47:33 - src.gui.unified_data_import_window - WARNING - 🔧 [方案B] mapping_tab没有_smart_save_user_modified_configs方法
2025-09-04 10:47:33 - src.gui.unified_data_import_window - WARNING - 🔧 [方案B] 智能保存失败，尝试强制保存
2025-09-04 10:47:33 - src.gui.unified_data_import_window - INFO - 🚨 [强制保存] 开始批量保存所有字段的当前配置状态
2025-09-04 10:47:33 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:47:33 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:47:33.179 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.序号 (版本: v1756954053)
2025-09-04 10:47:33.257 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.工号 (版本: v1756954053)
2025-09-04 10:47:33.351 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.姓名 (版本: v1756954053)
2025-09-04 10:47:33.429 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.部门名称 (版本: v1756954053)
2025-09-04 10:47:33.507 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.人员类别 (版本: v1756954053)
2025-09-04 10:47:33.585 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.人员类别代码 (版本: v1756954053)
2025-09-04 10:47:33.679 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.2025年岗位工资 (版本: v1756954053)
2025-09-04 10:47:33.757 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.2025年校龄工资 (版本: v1756954053)
2025-09-04 10:47:33.835 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.津贴 (版本: v1756954053)
2025-09-04 10:47:33.931 | ERROR    | src.modules.data_import.config_sync_manager:_save_config_file:1199 | 文件替换权限错误: [WinError 5] 拒绝访问。: 'state\\data\\field_mappings.json.tmp' -> 'state\\data\\field_mappings.json'
2025-09-04 10:47:33.991 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.结余津贴 (版本: v1756954053)
2025-09-04 10:47:34.070 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.2025年基础性绩效 (版本: v1756954054)
2025-09-04 10:47:34.148 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.卫生费 (版本: v1756954054)
2025-09-04 10:47:34.241 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.2025年生活补贴 (版本: v1756954054)
2025-09-04 10:47:34.335 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.车补 (版本: v1756954054)
2025-09-04 10:47:34.429 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.2025年奖励性绩效预发 (版本: v1756954054)
2025-09-04 10:47:34.523 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.补发 (版本: v1756954054)
2025-09-04 10:47:34.616 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.借支 (版本: v1756954054)
2025-09-04 10:47:34.695 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.应发工资 (版本: v1756954054)
2025-09-04 10:47:34.788 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.2025公积金 (版本: v1756954054)
2025-09-04 10:47:34.866 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.保险扣款 (版本: v1756954054)
2025-09-04 10:47:34.960 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.代扣代存养老保险 (版本: v1756954054)
2025-09-04 10:47:34 - src.gui.unified_data_import_window - INFO - 🚨 [强制保存] 完成，成功保存 21/21 个字段配置
2025-09-04 10:47:34 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 已强制保存当前Sheet的所有字段配置
2025-09-04 10:47:34.960 | INFO     | src.modules.data_import.formatting_engine:clear_temporary_field_types:866 | 🔧 [方案一实施] 已清理 0 个临时字段类型
2025-09-04 10:47:34 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 已清理临时字段类型
2025-09-04 10:47:34.960 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '离休人员工资表' 创建默认配置
2025-09-04 10:47:34.960 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-09-04 10:47:34.960 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 离休人员工资表
2025-09-04 10:47:34.960 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 10:47:34.960 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 10:47:34.960 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 10:47:35.069 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-04 10:47:35.069 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 10:47:35.069 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-04 10:47:35.069 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 16列
2025-09-04 10:47:35.069 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 16列
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 为Sheet '离休人员工资表' 获取到 16 个字段
2025-09-04 10:47:35 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 加载Sheet '离休人员工资表' 配置，表名: mapping_config_default_sheet
2025-09-04 10:47:35.069 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:538 |  [配置提取] 加载完整字段配置: mapping_config_default_sheet, 字段数: 32
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 找到 32 个已保存的字段配置
2025-09-04 10:47:35 - src.gui.unified_data_import_window - ERROR - 🔧 [方案B] 加载Sheet配置失败: 'UnifiedMappingConfigWidget' object has no attribute '_enhance_configs_with_version_control'
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 加载Excel字段: 16 个字段, 表类型: 💰 工资表
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '序号' 使用智能推断类型: text_string
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '序号' (第0行) 设置即时保存机制
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '序号' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '人员代码' 使用智能推断类型: code_string
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '人员代码' (第1行) 设置即时保存机制
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '人员代码' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '姓名' 使用智能推断类型: name_string
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '姓名' (第2行) 设置即时保存机制
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '姓名' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '部门名称' 使用智能推断类型: text_string
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '部门名称' (第3行) 设置即时保存机制
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '部门名称' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '基本
离休费' 使用智能推断类型: salary_float
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '基本离休费' (第4行) 设置即时保存机制
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '基本离休费' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '结余
津贴' 使用智能推断类型: salary_float
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '结余津贴' (第5行) 设置即时保存机制
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '结余津贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '生活
补贴' 使用智能推断类型: salary_float
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '生活补贴' (第6行) 设置即时保存机制
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '生活补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '住房
补贴' 使用智能推断类型: salary_float
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '住房补贴' (第7行) 设置即时保存机制
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '住房补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '物业
补贴' 使用智能推断类型: salary_float
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '物业补贴' (第8行) 设置即时保存机制
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '物业补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '离休
补贴' 使用智能推断类型: salary_float
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '离休补贴' (第9行) 设置即时保存机制
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '离休补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '护理费' 使用智能推断类型: salary_float
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '护理费' (第10行) 设置即时保存机制
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '护理费' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '增发一次
性生活补贴' 使用智能推断类型: salary_float
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '增发一次性生活补贴' (第11行) 设置即时保存机制
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '增发一次性生活补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '补发' 使用智能推断类型: text_string
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '补发' (第12行) 设置即时保存机制
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '补发' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '合计' 使用智能推断类型: text_string
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '合计' (第13行) 设置即时保存机制
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '合计' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '借支' 使用智能推断类型: text_string
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '借支' (第14行) 设置即时保存机制
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '借支' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '备注' 使用智能推断类型: text_string
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '备注' (第15行) 设置即时保存机制
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '备注' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:35 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段映射表格创建完成: 16 行
2025-09-04 10:47:35 - src.gui.core.smart_mapping_engine - INFO - 开始生成智能映射: 类型=💰 工资表, 字段数=16
2025-09-04 10:47:35 - src.gui.core.smart_mapping_engine - INFO - 智能映射生成完成: 高置信度 4 个, 中等置信度 0 个
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 智能映射生成成功
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] Sheet '离休人员工资表' 字段映射已更新
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: 离休人员工资表
2025-09-04 10:47:37.381 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 10:47:37.382 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 10:47:37.382 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 10:47:37.559 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-04 10:47:37.561 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 10:47:37.564 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-04 10:47:37.564 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-09-04 10:47:37.567 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-04 10:47:37.567 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-04 10:47:37.569 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 开始获取字段映射配置，表格行数: 16
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第0行字段: '序号'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '序号' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '序号', 'db_field': '序号'}
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第1行字段: '人员代码'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '代码', 数据: 'code_string'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '人员代码' 最终配置: {'field_type': 'code_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '人员代码', 'db_field': '人员代码'}
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第2行字段: '姓名'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '姓名', 数据: 'name_string'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '姓名' 最终配置: {'field_type': 'name_string', 'data_type': 'VARCHAR(100)', 'is_required': True, 'display_name': '姓名', 'db_field': '姓名'}
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第3行字段: '部门名称'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '部门名称' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '部门名称', 'db_field': '部门'}
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第4行字段: '基本离休费'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '基本离休费' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '基本\n离休费', 'db_field': '基本\n离休费'}
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第5行字段: '结余津贴'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '结余津贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '结余\n津贴', 'db_field': '津贴'}
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第6行字段: '生活补贴'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '生活补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '生活\n补贴', 'db_field': 'money'}
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第7行字段: '住房补贴'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '住房补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '住房\n补贴', 'db_field': 'money'}
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第8行字段: '物业补贴'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '物业补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '物业\n补贴', 'db_field': 'money'}
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第9行字段: '离休补贴'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '离休补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '离休\n补贴', 'db_field': 'money'}
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第10行字段: '护理费'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '护理费' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '护理费', 'db_field': '护理费'}
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第11行字段: '增发一次性生活补贴'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '增发一次性生活补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '增发一次\n性生活补贴', 'db_field': 'money'}
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第12行字段: '补发'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '补发' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '补发', 'db_field': '补发'}
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第13行字段: '合计'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '合计' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': True, 'display_name': '合计', 'db_field': '应发合计'}
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第14行字段: '借支'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '借支' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '借支', 'db_field': '借支'}
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第15行字段: '备注'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '备注' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '备注', 'db_field': '备注'}
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 获取字段映射配置完成，共 16 个字段
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [关键修复] 获取到 16 个字段映射配置
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 开始显示预览数据: 离休人员工资表, 数据行数: 2
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段映射配置: {'序号': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '人员代码': {'field_type': 'code_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '姓名': {'field_type': 'name_string', 'data_type': 'VARCHAR(100)', 'is_required': True}, '部门名称': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '基本离休费': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '结余津贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '生活补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '住房补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '物业补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '离休补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '护理费': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '增发一次性生活补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '补发': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '合计': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': True}, '借支': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '备注': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}}
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 获取格式化引擎成功
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 设置表格结构: 16 列, 2 行
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '序号' 映射到类型: text_string
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员代码' 映射到类型: code_string
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员代码' 格式化: '19289006.0' -> '19289006'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '姓名' 映射到类型: name_string
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '部门名称' 映射到类型: text_string
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '基本
离休费' 无映射配置，使用原值
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '结余
津贴' 无映射配置，使用原值
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '生活
补贴' 无映射配置，使用原值
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '住房
补贴' 无映射配置，使用原值
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '物业
补贴' 无映射配置，使用原值
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '离休
补贴' 无映射配置，使用原值
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '护理费' 映射到类型: salary_float
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '护理费' 格式化: '3800.0' -> '3,800.00'
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '增发一次
性生活补贴' 无映射配置，使用原值
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '补发' 映射到类型: text_string
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '合计' 映射到类型: text_string
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '借支' 映射到类型: text_string
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '备注' 映射到类型: text_string
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: 离休人员工资表 - 2 行
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 2 行, 16 列
2025-09-04 10:47:37 - src.gui.unified_data_import_window - INFO - 已加载Sheet '离休人员工资表' 的预览数据: 2 行
2025-09-04 10:47:51 - src.gui.unified_data_import_window - INFO - 选中Sheet: A岗职工
2025-09-04 10:47:51 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 当前Sheet变化: A岗职工
2025-09-04 10:47:51 - src.gui.unified_data_import_window - WARNING - 🔧 [方案B] mapping_tab没有_smart_save_user_modified_configs方法
2025-09-04 10:47:51 - src.gui.unified_data_import_window - WARNING - 🔧 [方案B] 智能保存失败，尝试强制保存
2025-09-04 10:47:51 - src.gui.unified_data_import_window - INFO - 🚨 [强制保存] 开始批量保存所有字段的当前配置状态
2025-09-04 10:47:51 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:47:51 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:47:51.943 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1054 | 🔧 [方案B] 用户修改配置已保存: mapping_config_default_sheet.序号 (版本: v1756954071)
2025-09-04 10:47:52.022 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.人员代码 (版本: v1756954071)
2025-09-04 10:47:52.115 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.姓名 (版本: v1756954072)
2025-09-04 10:47:52.209 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.部门名称 (版本: v1756954072)
2025-09-04 10:47:52.303 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.基本离休费 (版本: v1756954072)
2025-09-04 10:47:52.397 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.结余津贴 (版本: v1756954072)
2025-09-04 10:47:52.475 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.生活补贴 (版本: v1756954072)
2025-09-04 10:47:52.568 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.住房补贴 (版本: v1756954072)
2025-09-04 10:47:52.662 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.物业补贴 (版本: v1756954072)
2025-09-04 10:47:52.756 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.离休补贴 (版本: v1756954072)
2025-09-04 10:47:52.834 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.护理费 (版本: v1756954072)
2025-09-04 10:47:52.943 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.增发一次性生活补贴 (版本: v1756954072)
2025-09-04 10:47:53.038 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.补发 (版本: v1756954072)
2025-09-04 10:47:53.131 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.合计 (版本: v1756954073)
2025-09-04 10:47:53.225 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.借支 (版本: v1756954073)
2025-09-04 10:47:53.319 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.备注 (版本: v1756954073)
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🚨 [强制保存] 完成，成功保存 16/16 个字段配置
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 已强制保存当前Sheet的所有字段配置
2025-09-04 10:47:53.319 | INFO     | src.modules.data_import.formatting_engine:clear_temporary_field_types:866 | 🔧 [方案一实施] 已清理 0 个临时字段类型
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 已清理临时字段类型
2025-09-04 10:47:53.319 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-09-04 10:47:53.319 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: A岗职工
2025-09-04 10:47:53.319 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 10:47:53.319 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 10:47:53.319 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 10:47:53.428 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-09-04 10:47:53.428 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 10:47:53.428 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-04 10:47:53.428 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 21列
2025-09-04 10:47:53.428 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 21列
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 为Sheet 'A岗职工' 获取到 21 个字段
2025-09-04 10:47:53 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 加载Sheet 'A岗职工' 配置，表名: mapping_config_default_sheet
2025-09-04 10:47:53.443 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:538 |  [配置提取] 加载完整字段配置: mapping_config_default_sheet, 字段数: 32
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 找到 32 个已保存的字段配置
2025-09-04 10:47:53 - src.gui.unified_data_import_window - ERROR - 🔧 [方案B] 加载Sheet配置失败: 'UnifiedMappingConfigWidget' object has no attribute '_enhance_configs_with_version_control'
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 加载Excel字段: 21 个字段, 表类型: 💰 工资表
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '序号' 使用智能推断类型: text_string
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '序号' (第0行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '序号' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '工号' 使用智能推断类型: employee_id_string
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '工号' (第1行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '工号' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '姓名' 使用智能推断类型: name_string
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '姓名' (第2行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '姓名' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '部门名称' 使用智能推断类型: text_string
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '部门名称' (第3行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '部门名称' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '人员类别' 使用智能推断类型: personnel_category_code
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '人员类别' (第4行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '人员类别' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '人员类别代码' 使用智能推断类型: personnel_category_code
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '人员类别代码' (第5行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '人员类别代码' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2025年岗位工资' 使用智能推断类型: salary_float
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2025年岗位工资' (第6行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2025年岗位工资' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2025年校龄工资' 使用智能推断类型: salary_float
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2025年校龄工资' (第7行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2025年校龄工资' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '津贴' 使用智能推断类型: salary_float
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '津贴' (第8行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '津贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '结余津贴' 使用智能推断类型: salary_float
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '结余津贴' (第9行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '结余津贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2025年基础性绩效' 使用智能推断类型: salary_float
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2025年基础性绩效' (第10行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2025年基础性绩效' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '卫生费' 使用智能推断类型: salary_float
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '卫生费' (第11行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '卫生费' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2025年生活补贴' 使用智能推断类型: salary_float
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2025年生活补贴' (第12行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2025年生活补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '车补' 使用智能推断类型: text_string
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '车补' (第13行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '车补' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2025年奖励性绩效预发' 使用智能推断类型: salary_float
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2025年奖励性绩效预发' (第14行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2025年奖励性绩效预发' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '补发' 使用智能推断类型: text_string
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '补发' (第15行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '补发' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '借支' 使用智能推断类型: text_string
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '借支' (第16行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '借支' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '应发工资' 使用智能推断类型: salary_float
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '应发工资' (第17行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '应发工资' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2025公积金' 使用智能推断类型: text_string
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2025公积金' (第18行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2025公积金' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '保险扣款' 使用智能推断类型: text_string
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '保险扣款' (第19行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '保险扣款' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '代扣代存养老保险' 使用智能推断类型: text_string
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '代扣代存养老保险' (第20行) 设置即时保存机制
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '代扣代存养老保险' currentTextChanged -> 即时保存处理函数
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:47:53 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段映射表格创建完成: 21 行
2025-09-04 10:47:53 - src.gui.core.smart_mapping_engine - INFO - 开始生成智能映射: 类型=💰 工资表, 字段数=21
2025-09-04 10:47:53 - src.gui.core.smart_mapping_engine - INFO - 智能映射生成完成: 高置信度 5 个, 中等置信度 3 个
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 智能映射生成成功
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] Sheet 'A岗职工' 字段映射已更新
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: A岗职工
2025-09-04 10:47:57.471 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 10:47:57.471 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 10:47:57.472 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 10:47:57.582 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-09-04 10:47:57.584 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 10:47:57.586 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-09-04 10:47:57.586 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 63行 x 21列
2025-09-04 10:47:57.589 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-04 10:47:57.590 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-09-04 10:47:57.594 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 62行 × 21列
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 开始获取字段映射配置，表格行数: 21
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第0行字段: '序号'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '序号' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '序号', 'db_field': '序号'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第1行字段: '工号'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工号', 数据: 'employee_id_string'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '工号' 最终配置: {'field_type': 'employee_id_string', 'data_type': 'VARCHAR(100)', 'is_required': True, 'display_name': '工号', 'db_field': '工号'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第2行字段: '姓名'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '姓名', 数据: 'name_string'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '姓名' 最终配置: {'field_type': 'name_string', 'data_type': 'VARCHAR(100)', 'is_required': True, 'display_name': '姓名', 'db_field': '姓名'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第3行字段: '部门名称'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '部门名称' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '部门名称', 'db_field': '部门'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第4行字段: '人员类别'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '人员类别代码', 数据: 'personnel_category_code'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '人员类别' 最终配置: {'field_type': 'personnel_category_code', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '人员类别', 'db_field': '人员类别'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第5行字段: '人员类别代码'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '人员类别代码', 数据: 'personnel_category_code'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '人员类别代码' 最终配置: {'field_type': 'personnel_category_code', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '人员类别代码', 'db_field': '人员类别代码'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第6行字段: '2025年岗位工资'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年岗位工资' 最终配置: {'field_type': 'salary_float', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '2025年岗位工资', 'db_field': 'position'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第7行字段: '2025年校龄工资'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年校龄工资' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年校龄工资', 'db_field': 'money'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第8行字段: '津贴'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '津贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '津贴', 'db_field': '津贴'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第9行字段: '结余津贴'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '结余津贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '结余津贴', 'db_field': 'money'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第10行字段: '2025年基础性绩效'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年基础性绩效' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年基础性绩效', 'db_field': 'money'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第11行字段: '卫生费'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '卫生费' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '卫生费', 'db_field': '卫生费'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第12行字段: '2025年生活补贴'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年生活补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年生活补贴', 'db_field': 'money'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第13行字段: '车补'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '车补' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '车补', 'db_field': '车补'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第14行字段: '2025年奖励性绩效预发'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年奖励性绩效预发' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年奖励性绩效预发', 'db_field': 'money'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第15行字段: '补发'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '补发' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '补发', 'db_field': '补发'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第16行字段: '借支'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '借支' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '借支', 'db_field': '借支'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第17行字段: '应发工资'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '应发工资' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': True, 'display_name': '应发工资', 'db_field': 'money'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第18行字段: '2025公积金'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025公积金' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025公积金', 'db_field': '2025公积金'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第19行字段: '保险扣款'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '保险扣款' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '保险扣款', 'db_field': '扣款'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第20行字段: '代扣代存养老保险'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '代扣代存养老保险' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '代扣代存养老保险', 'db_field': 'money'}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 获取字段映射配置完成，共 21 个字段
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [关键修复] 获取到 21 个字段映射配置
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 开始显示预览数据: A岗职工, 数据行数: 62
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段映射配置: {'序号': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '工号': {'field_type': 'employee_id_string', 'data_type': 'VARCHAR(100)', 'is_required': True}, '姓名': {'field_type': 'name_string', 'data_type': 'VARCHAR(100)', 'is_required': True}, '部门名称': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '人员类别': {'field_type': 'personnel_category_code', 'data_type': 'VARCHAR(100)', 'is_required': False}, '人员类别代码': {'field_type': 'personnel_category_code', 'data_type': 'VARCHAR(100)', 'is_required': False}, '2025年岗位工资': {'field_type': 'salary_float', 'data_type': 'VARCHAR(100)', 'is_required': False}, '2025年校龄工资': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '津贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '结余津贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '2025年基础性绩效': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '卫生费': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '2025年生活补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '车补': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '2025年奖励性绩效预发': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '补发': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '借支': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '应发工资': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': True}, '2025公积金': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '保险扣款': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '代扣代存养老保险': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}}
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 获取格式化引擎成功
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 设置表格结构: 21 列, 62 行
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '序号' 映射到类型: text_string
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '工号' 映射到类型: employee_id_string
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '工号' 格式化: '34660024.0' -> '34660024'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '姓名' 映射到类型: name_string
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '部门名称' 映射到类型: text_string
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员类别' 映射到类型: personnel_category_code
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员类别代码' 映射到类型: personnel_category_code
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员类别代码' 格式化: '1.0' -> '01'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年岗位工资' 映射到类型: salary_float
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年岗位工资' 格式化: '1720.0' -> '1,720.00'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年校龄工资' 映射到类型: salary_float
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年校龄工资' 格式化: '1061.0' -> '1,061.00'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '津贴' 映射到类型: salary_float
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '津贴' 格式化: '0.0' -> '0.00'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '结余津贴' 映射到类型: salary_float
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '结余津贴' 格式化: '56.0' -> '56.00'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年基础性绩效' 映射到类型: salary_float
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年基础性绩效' 格式化: '2696.0' -> '2,696.00'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '卫生费' 映射到类型: salary_float
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '卫生费' 格式化: '20.0' -> '20.00'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年生活补贴' 映射到类型: salary_float
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年生活补贴' 格式化: '493.0' -> '493.00'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '车补' 映射到类型: text_string
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年奖励性绩效预发' 映射到类型: salary_float
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年奖励性绩效预发' 格式化: '1500.0' -> '1,500.00'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '补发' 映射到类型: text_string
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '借支' 映射到类型: text_string
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '应发工资' 映射到类型: salary_float
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '应发工资' 格式化: '7546.0' -> '7,546.00'
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025公积金' 映射到类型: text_string
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '保险扣款' 映射到类型: text_string
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '代扣代存养老保险' 映射到类型: text_string
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: A岗职工 - 62 行
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 62 行, 21 列
2025-09-04 10:47:57 - src.gui.unified_data_import_window - INFO - 已加载Sheet 'A岗职工' 的预览数据: 62 行
2025-09-04 10:48:09 - src.gui.unified_data_import_window - INFO - 选中Sheet: 离休人员工资表
2025-09-04 10:48:09 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 当前Sheet变化: 离休人员工资表
2025-09-04 10:48:09 - src.gui.unified_data_import_window - WARNING - 🔧 [方案B] mapping_tab没有_smart_save_user_modified_configs方法
2025-09-04 10:48:09 - src.gui.unified_data_import_window - WARNING - 🔧 [方案B] 智能保存失败，尝试强制保存
2025-09-04 10:48:09 - src.gui.unified_data_import_window - INFO - 🚨 [强制保存] 开始批量保存所有字段的当前配置状态
2025-09-04 10:48:09 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:48:09 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:48:09.733 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1054 | 🔧 [方案B] 用户修改配置已保存: mapping_config_default_sheet.序号 (版本: v1756954089)
2025-09-04 10:48:09.827 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.工号 (版本: v1756954089)
2025-09-04 10:48:09.921 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.姓名 (版本: v1756954089)
2025-09-04 10:48:10.015 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.部门名称 (版本: v1756954089)
2025-09-04 10:48:10.124 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1054 | 🔧 [方案B] 用户修改配置已保存: mapping_config_default_sheet.人员类别 (版本: v1756954090)
2025-09-04 10:48:10.218 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.人员类别代码 (版本: v1756954090)
2025-09-04 10:48:10.312 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.2025年岗位工资 (版本: v1756954090)
2025-09-04 10:48:10.405 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.2025年校龄工资 (版本: v1756954090)
2025-09-04 10:48:10.499 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.津贴 (版本: v1756954090)
2025-09-04 10:48:10.593 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.结余津贴 (版本: v1756954090)
2025-09-04 10:48:10.686 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.2025年基础性绩效 (版本: v1756954090)
2025-09-04 10:48:10.780 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.卫生费 (版本: v1756954090)
2025-09-04 10:48:10.876 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.2025年生活补贴 (版本: v1756954090)
2025-09-04 10:48:10.983 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1054 | 🔧 [方案B] 用户修改配置已保存: mapping_config_default_sheet.车补 (版本: v1756954090)
2025-09-04 10:48:11.079 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.2025年奖励性绩效预发 (版本: v1756954091)
2025-09-04 10:48:11.171 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.补发 (版本: v1756954091)
2025-09-04 10:48:11.265 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.借支 (版本: v1756954091)
2025-09-04 10:48:11.360 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.应发工资 (版本: v1756954091)
2025-09-04 10:48:11.468 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.2025公积金 (版本: v1756954091)
2025-09-04 10:48:11.562 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.保险扣款 (版本: v1756954091)
2025-09-04 10:48:11.655 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.代扣代存养老保险 (版本: v1756954091)
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🚨 [强制保存] 完成，成功保存 21/21 个字段配置
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 已强制保存当前Sheet的所有字段配置
2025-09-04 10:48:11.655 | INFO     | src.modules.data_import.formatting_engine:clear_temporary_field_types:866 | 🔧 [方案一实施] 已清理 0 个临时字段类型
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 已清理临时字段类型
2025-09-04 10:48:11.671 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-09-04 10:48:11.671 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 离休人员工资表
2025-09-04 10:48:11.671 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 10:48:11.671 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 10:48:11.671 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 10:48:11.765 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-04 10:48:11.765 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 10:48:11.765 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-04 10:48:11.765 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 16列
2025-09-04 10:48:11.780 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 16列
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 为Sheet '离休人员工资表' 获取到 16 个字段
2025-09-04 10:48:11 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 加载Sheet '离休人员工资表' 配置，表名: mapping_config_default_sheet
2025-09-04 10:48:11.780 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:538 |  [配置提取] 加载完整字段配置: mapping_config_default_sheet, 字段数: 32
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 找到 32 个已保存的字段配置
2025-09-04 10:48:11 - src.gui.unified_data_import_window - ERROR - 🔧 [方案B] 加载Sheet配置失败: 'UnifiedMappingConfigWidget' object has no attribute '_enhance_configs_with_version_control'
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 加载Excel字段: 16 个字段, 表类型: 💰 工资表
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '序号' 使用智能推断类型: text_string
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '序号' (第0行) 设置即时保存机制
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '序号' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '人员代码' 使用智能推断类型: code_string
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '人员代码' (第1行) 设置即时保存机制
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '人员代码' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '姓名' 使用智能推断类型: name_string
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '姓名' (第2行) 设置即时保存机制
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '姓名' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '部门名称' 使用智能推断类型: text_string
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '部门名称' (第3行) 设置即时保存机制
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '部门名称' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '基本
离休费' 使用智能推断类型: salary_float
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '基本离休费' (第4行) 设置即时保存机制
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '基本离休费' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '结余
津贴' 使用智能推断类型: salary_float
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '结余津贴' (第5行) 设置即时保存机制
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '结余津贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '生活
补贴' 使用智能推断类型: salary_float
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '生活补贴' (第6行) 设置即时保存机制
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '生活补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '住房
补贴' 使用智能推断类型: salary_float
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '住房补贴' (第7行) 设置即时保存机制
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '住房补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '物业
补贴' 使用智能推断类型: salary_float
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '物业补贴' (第8行) 设置即时保存机制
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '物业补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '离休
补贴' 使用智能推断类型: salary_float
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '离休补贴' (第9行) 设置即时保存机制
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '离休补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '护理费' 使用智能推断类型: salary_float
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '护理费' (第10行) 设置即时保存机制
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '护理费' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '增发一次
性生活补贴' 使用智能推断类型: salary_float
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '增发一次性生活补贴' (第11行) 设置即时保存机制
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '增发一次性生活补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '补发' 使用智能推断类型: text_string
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '补发' (第12行) 设置即时保存机制
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '补发' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '合计' 使用智能推断类型: text_string
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '合计' (第13行) 设置即时保存机制
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '合计' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '借支' 使用智能推断类型: text_string
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '借支' (第14行) 设置即时保存机制
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '借支' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '备注' 使用智能推断类型: text_string
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '备注' (第15行) 设置即时保存机制
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '备注' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:11 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段映射表格创建完成: 16 行
2025-09-04 10:48:11 - src.gui.core.smart_mapping_engine - INFO - 开始生成智能映射: 类型=💰 工资表, 字段数=16
2025-09-04 10:48:11 - src.gui.core.smart_mapping_engine - INFO - 智能映射生成完成: 高置信度 4 个, 中等置信度 0 个
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 智能映射生成成功
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] Sheet '离休人员工资表' 字段映射已更新
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: 离休人员工资表
2025-09-04 10:48:14.141 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 10:48:14.142 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 10:48:14.142 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 10:48:14.248 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-09-04 10:48:14.251 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 10:48:14.253 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-09-04 10:48:14.253 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-09-04 10:48:14.256 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-04 10:48:14.256 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-09-04 10:48:14.258 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 开始获取字段映射配置，表格行数: 16
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第0行字段: '序号'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '序号' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '序号', 'db_field': '序号'}
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第1行字段: '人员代码'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '代码', 数据: 'code_string'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '人员代码' 最终配置: {'field_type': 'code_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '人员代码', 'db_field': '人员代码'}
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第2行字段: '姓名'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '姓名', 数据: 'name_string'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '姓名' 最终配置: {'field_type': 'name_string', 'data_type': 'VARCHAR(100)', 'is_required': True, 'display_name': '姓名', 'db_field': '姓名'}
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第3行字段: '部门名称'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '部门名称' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '部门名称', 'db_field': '部门'}
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第4行字段: '基本离休费'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '基本离休费' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '基本\n离休费', 'db_field': '基本\n离休费'}
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第5行字段: '结余津贴'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '结余津贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '结余\n津贴', 'db_field': '津贴'}
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第6行字段: '生活补贴'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '生活补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '生活\n补贴', 'db_field': 'money'}
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第7行字段: '住房补贴'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '住房补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '住房\n补贴', 'db_field': 'money'}
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第8行字段: '物业补贴'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '物业补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '物业\n补贴', 'db_field': 'money'}
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第9行字段: '离休补贴'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '离休补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '离休\n补贴', 'db_field': 'money'}
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第10行字段: '护理费'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '护理费' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '护理费', 'db_field': '护理费'}
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第11行字段: '增发一次性生活补贴'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '增发一次性生活补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '增发一次\n性生活补贴', 'db_field': 'money'}
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第12行字段: '补发'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '补发' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '补发', 'db_field': '补发'}
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第13行字段: '合计'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '合计' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': True, 'display_name': '合计', 'db_field': '应发合计'}
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第14行字段: '借支'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '借支' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '借支', 'db_field': '借支'}
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第15行字段: '备注'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '备注' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '备注', 'db_field': '备注'}
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 获取字段映射配置完成，共 16 个字段
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [关键修复] 获取到 16 个字段映射配置
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 开始显示预览数据: 离休人员工资表, 数据行数: 2
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段映射配置: {'序号': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '人员代码': {'field_type': 'code_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '姓名': {'field_type': 'name_string', 'data_type': 'VARCHAR(100)', 'is_required': True}, '部门名称': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '基本离休费': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '结余津贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '生活补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '住房补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '物业补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '离休补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '护理费': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '增发一次性生活补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '补发': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '合计': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': True}, '借支': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '备注': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}}
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 获取格式化引擎成功
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 设置表格结构: 16 列, 2 行
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '序号' 映射到类型: text_string
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员代码' 映射到类型: code_string
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员代码' 格式化: '19289006.0' -> '19289006'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '姓名' 映射到类型: name_string
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '部门名称' 映射到类型: text_string
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '基本
离休费' 无映射配置，使用原值
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '结余
津贴' 无映射配置，使用原值
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '生活
补贴' 无映射配置，使用原值
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '住房
补贴' 无映射配置，使用原值
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '物业
补贴' 无映射配置，使用原值
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '离休
补贴' 无映射配置，使用原值
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '护理费' 映射到类型: salary_float
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '护理费' 格式化: '3800.0' -> '3,800.00'
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '增发一次
性生活补贴' 无映射配置，使用原值
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '补发' 映射到类型: text_string
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '合计' 映射到类型: text_string
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '借支' 映射到类型: text_string
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '备注' 映射到类型: text_string
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: 离休人员工资表 - 2 行
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 2 行, 16 列
2025-09-04 10:48:14 - src.gui.unified_data_import_window - INFO - 已加载Sheet '离休人员工资表' 的预览数据: 2 行
2025-09-04 10:48:16 - src.gui.unified_data_import_window - ERROR - 🔧 [方案B] 字段类型变化处理失败: 'UnifiedMappingConfigWidget' object has no attribute '_save_field_type_immediately_with_version_control'
2025-09-04 10:48:16 - src.gui.unified_data_import_window - INFO - 字段类型已回滚: 序号 -> integer
2025-09-04 10:48:17 - src.gui.unified_data_import_window - INFO - 💾 [防抖保存] *** 执行延迟保存 *** 字段: '序号', 类型: integer
2025-09-04 10:48:17 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:48:17 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:48:17.490 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1054 | 🔧 [方案B] 用户修改配置已保存: mapping_config_default_sheet.序号 (版本: v1756954097)
2025-09-04 10:48:17 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 字段配置保存成功: mapping_config_default_sheet.序号
2025-09-04 10:48:17 - src.gui.unified_data_import_window - INFO - 💾 [防抖保存] 保存结果: 成功
2025-09-04 10:48:17.490 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 序号
2025-09-04 10:48:21 - src.gui.unified_data_import_window - ERROR - 🔧 [方案B] 字段类型变化处理失败: 'UnifiedMappingConfigWidget' object has no attribute '_save_field_type_immediately_with_version_control'
2025-09-04 10:48:21 - src.gui.unified_data_import_window - INFO - 字段类型已回滚: 人员代码 -> employee_id_string
2025-09-04 10:48:22 - src.gui.unified_data_import_window - INFO - 💾 [防抖保存] *** 执行延迟保存 *** 字段: '人员代码', 类型: employee_id_string
2025-09-04 10:48:22 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:48:22 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:48:22.335 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1054 | 🔧 [方案B] 用户修改配置已保存: mapping_config_default_sheet.人员代码 (版本: v1756954102)
2025-09-04 10:48:22 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 字段配置保存成功: mapping_config_default_sheet.人员代码
2025-09-04 10:48:22 - src.gui.unified_data_import_window - INFO - 💾 [防抖保存] 保存结果: 成功
2025-09-04 10:48:22.350 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员代码
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔄 [修复] 选项卡切换到: 👁️ 预览验证 (索引: 2)
2025-09-04 10:48:25 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:48:25.896 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:448 | 字段映射保存成功: mapping_config_default_sheet
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_default_sheet
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔄 [修复] 切换到预览选项卡，开始刷新格式化
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 开始获取字段映射配置，表格行数: 16
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第0行字段: '序号'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '整数', 数据: 'integer'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '序号' 最终配置: {'field_type': 'integer', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '序号', 'db_field': '序号'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第1行字段: '人员代码'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工号', 数据: 'employee_id_string'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '人员代码' 最终配置: {'field_type': 'employee_id_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '人员代码', 'db_field': '人员代码'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第2行字段: '姓名'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '姓名', 数据: 'name_string'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '姓名' 最终配置: {'field_type': 'name_string', 'data_type': 'VARCHAR(100)', 'is_required': True, 'display_name': '姓名', 'db_field': '姓名'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第3行字段: '部门名称'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '部门名称' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '部门名称', 'db_field': '部门'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第4行字段: '基本离休费'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '基本离休费' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '基本\n离休费', 'db_field': '基本\n离休费'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第5行字段: '结余津贴'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '结余津贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '结余\n津贴', 'db_field': '津贴'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第6行字段: '生活补贴'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '生活补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '生活\n补贴', 'db_field': 'money'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第7行字段: '住房补贴'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '住房补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '住房\n补贴', 'db_field': 'money'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第8行字段: '物业补贴'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '物业补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '物业\n补贴', 'db_field': 'money'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第9行字段: '离休补贴'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '离休补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '离休\n补贴', 'db_field': 'money'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第10行字段: '护理费'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '护理费' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '护理费', 'db_field': '护理费'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第11行字段: '增发一次性生活补贴'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '增发一次性生活补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '增发一次\n性生活补贴', 'db_field': 'money'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第12行字段: '补发'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '补发' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '补发', 'db_field': '补发'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第13行字段: '合计'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '合计' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': True, 'display_name': '合计', 'db_field': '应发合计'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第14行字段: '借支'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '借支' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '借支', 'db_field': '借支'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第15行字段: '备注'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '备注' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '备注', 'db_field': '备注'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 获取字段映射配置完成，共 16 个字段
2025-09-04 10:48:25.928 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 序号
2025-09-04 10:48:25.928 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员代码
2025-09-04 10:48:25.928 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 姓名
2025-09-04 10:48:25.928 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 部门名称
2025-09-04 10:48:25.928 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 基本离休费
2025-09-04 10:48:25.928 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 结余津贴
2025-09-04 10:48:25.928 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 生活补贴
2025-09-04 10:48:25.928 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 住房补贴
2025-09-04 10:48:25.928 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 物业补贴
2025-09-04 10:48:25.928 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 离休补贴
2025-09-04 10:48:25.928 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 护理费
2025-09-04 10:48:25.928 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 增发一次性生活补贴
2025-09-04 10:48:25.928 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 补发
2025-09-04 10:48:25.928 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 合计
2025-09-04 10:48:25.928 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 借支
2025-09-04 10:48:25.928 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 备注
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔄 [修复] 使用缓存的预览数据
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 开始获取字段映射配置，表格行数: 16
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第0行字段: '序号'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '整数', 数据: 'integer'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '序号' 最终配置: {'field_type': 'integer', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '序号', 'db_field': '序号'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第1行字段: '人员代码'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工号', 数据: 'employee_id_string'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '人员代码' 最终配置: {'field_type': 'employee_id_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '人员代码', 'db_field': '人员代码'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第2行字段: '姓名'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '姓名', 数据: 'name_string'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '姓名' 最终配置: {'field_type': 'name_string', 'data_type': 'VARCHAR(100)', 'is_required': True, 'display_name': '姓名', 'db_field': '姓名'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第3行字段: '部门名称'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '部门名称' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '部门名称', 'db_field': '部门'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第4行字段: '基本离休费'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '基本离休费' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '基本\n离休费', 'db_field': '基本\n离休费'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第5行字段: '结余津贴'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '结余津贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '结余\n津贴', 'db_field': '津贴'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第6行字段: '生活补贴'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '生活补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '生活\n补贴', 'db_field': 'money'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第7行字段: '住房补贴'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '住房补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '住房\n补贴', 'db_field': 'money'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第8行字段: '物业补贴'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '物业补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '物业\n补贴', 'db_field': 'money'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第9行字段: '离休补贴'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '离休补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '离休\n补贴', 'db_field': 'money'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第10行字段: '护理费'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '护理费' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '护理费', 'db_field': '护理费'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第11行字段: '增发一次性生活补贴'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '增发一次性生活补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '增发一次\n性生活补贴', 'db_field': 'money'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第12行字段: '补发'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '补发' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '补发', 'db_field': '补发'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第13行字段: '合计'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '合计' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': True, 'display_name': '合计', 'db_field': '应发合计'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第14行字段: '借支'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '借支' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '借支', 'db_field': '借支'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第15行字段: '备注'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '备注' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '备注', 'db_field': '备注'}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 获取字段映射配置完成，共 16 个字段
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔄 [修复] 获取到字段映射配置: 16 个字段
2025-09-04 10:48:25.943 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 序号
2025-09-04 10:48:25.943 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 人员代码
2025-09-04 10:48:25.943 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 姓名
2025-09-04 10:48:25.943 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 部门名称
2025-09-04 10:48:25.943 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 基本离休费
2025-09-04 10:48:25.943 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 结余津贴
2025-09-04 10:48:25.943 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 生活补贴
2025-09-04 10:48:25.943 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 住房补贴
2025-09-04 10:48:25.943 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 物业补贴
2025-09-04 10:48:25.943 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 离休补贴
2025-09-04 10:48:25.943 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 护理费
2025-09-04 10:48:25.963 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 增发一次性生活补贴
2025-09-04 10:48:25.963 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 补发
2025-09-04 10:48:25.963 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 合计
2025-09-04 10:48:25.963 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 借支
2025-09-04 10:48:25.963 | WARNING  | src.modules.data_import.formatting_engine:register_field_type:537 | 🔧 [方案一实施] 字段类型名称格式无效，跳过注册: 备注
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔄 [修复] 已注册 16 个字段类型到格式化引擎
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 开始显示预览数据: 离休人员工资表, 数据行数: 2
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段映射配置: {'序号': {'field_type': 'integer', 'data_type': 'VARCHAR(100)', 'is_required': False}, '人员代码': {'field_type': 'employee_id_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '姓名': {'field_type': 'name_string', 'data_type': 'VARCHAR(100)', 'is_required': True}, '部门名称': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '基本离休费': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '结余津贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '生活补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '住房补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '物业补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '离休补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '护理费': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '增发一次性生活补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '补发': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '合计': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': True}, '借支': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '备注': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}}
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 获取格式化引擎成功
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 设置表格结构: 16 列, 2 行
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '序号' 映射到类型: integer
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员代码' 映射到类型: employee_id_string
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员代码' 格式化: '19289006.0' -> '19289006'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '姓名' 映射到类型: name_string
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '部门名称' 映射到类型: text_string
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '基本
离休费' 无映射配置，使用原值
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '结余
津贴' 无映射配置，使用原值
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '生活
补贴' 无映射配置，使用原值
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '住房
补贴' 无映射配置，使用原值
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '物业
补贴' 无映射配置，使用原值
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '离休
补贴' 无映射配置，使用原值
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '护理费' 映射到类型: salary_float
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '护理费' 格式化: '3800.0' -> '3,800.00'
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '增发一次
性生活补贴' 无映射配置，使用原值
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '补发' 映射到类型: text_string
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '合计' 映射到类型: text_string
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '借支' 映射到类型: text_string
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '备注' 映射到类型: text_string
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: 离休人员工资表 - 2 行
2025-09-04 10:48:25 - src.gui.unified_data_import_window - INFO - 🔄 [修复] 已刷新格式化 - 16 个字段映射
2025-09-04 10:48:32 - src.gui.unified_data_import_window - INFO - 🔄 [修复] 选项卡切换到: 🔗 字段映射 (索引: 0)
2025-09-04 10:48:32 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:48:32 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:48:32.866 | INFO     | src.modules.data_import.config_sync_manager:save_mapping:448 | 字段映射保存成功: mapping_config_default_sheet
2025-09-04 10:48:32 - src.gui.unified_data_import_window - INFO - 字段映射实时保存成功: mapping_config_default_sheet
2025-09-04 10:48:55 - src.gui.unified_data_import_window - INFO - 选中Sheet: 全部在职人员工资表
2025-09-04 10:48:55.432 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '全部在职人员工资表' 创建默认配置
2025-09-04 10:48:55 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 当前Sheet变化: 全部在职人员工资表
2025-09-04 10:48:55 - src.gui.unified_data_import_window - WARNING - 🔧 [方案B] mapping_tab没有_smart_save_user_modified_configs方法
2025-09-04 10:48:55 - src.gui.unified_data_import_window - WARNING - 🔧 [方案B] 智能保存失败，尝试强制保存
2025-09-04 10:48:55 - src.gui.unified_data_import_window - INFO - 🚨 [强制保存] 开始批量保存所有字段的当前配置状态
2025-09-04 10:48:55 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:48:55 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:48:55.527 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.序号 (版本: v1756954135)
2025-09-04 10:48:55.620 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.人员代码 (版本: v1756954135)
2025-09-04 10:48:55.713 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.姓名 (版本: v1756954135)
2025-09-04 10:48:55.808 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.部门名称 (版本: v1756954135)
2025-09-04 10:48:55.901 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.基本离休费 (版本: v1756954135)
2025-09-04 10:48:55.995 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.结余津贴 (版本: v1756954135)
2025-09-04 10:48:56.089 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.生活补贴 (版本: v1756954136)
2025-09-04 10:48:56.182 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.住房补贴 (版本: v1756954136)
2025-09-04 10:48:56.280 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.物业补贴 (版本: v1756954136)
2025-09-04 10:48:56.432 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.离休补贴 (版本: v1756954136)
2025-09-04 10:48:56.526 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.护理费 (版本: v1756954136)
2025-09-04 10:48:56.636 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.增发一次性生活补贴 (版本: v1756954136)
2025-09-04 10:48:56.730 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.补发 (版本: v1756954136)
2025-09-04 10:48:56.823 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.合计 (版本: v1756954136)
2025-09-04 10:48:56.917 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.借支 (版本: v1756954136)
2025-09-04 10:48:57.011 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.备注 (版本: v1756954136)
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🚨 [强制保存] 完成，成功保存 16/16 个字段配置
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 已强制保存当前Sheet的所有字段配置
2025-09-04 10:48:57.011 | INFO     | src.modules.data_import.formatting_engine:clear_temporary_field_types:866 | 🔧 [方案一实施] 已清理 0 个临时字段类型
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 已清理临时字段类型
2025-09-04 10:48:57.011 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '全部在职人员工资表' 创建默认配置
2025-09-04 10:48:57.011 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-09-04 10:48:57.011 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 全部在职人员工资表
2025-09-04 10:48:57.026 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 10:48:57.026 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 10:48:57.026 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 10:48:57.120 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-09-04 10:48:57.120 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 10:48:57.120 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-09-04 10:48:57.120 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 23列
2025-09-04 10:48:57.120 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 23列
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 为Sheet '全部在职人员工资表' 获取到 23 个字段
2025-09-04 10:48:57 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 加载Sheet '全部在职人员工资表' 配置，表名: mapping_config_default_sheet
2025-09-04 10:48:57.120 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:538 |  [配置提取] 加载完整字段配置: mapping_config_default_sheet, 字段数: 32
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 找到 32 个已保存的字段配置
2025-09-04 10:48:57 - src.gui.unified_data_import_window - ERROR - 🔧 [方案B] 加载Sheet配置失败: 'UnifiedMappingConfigWidget' object has no attribute '_enhance_configs_with_version_control'
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 加载Excel字段: 23 个字段, 表类型: 💰 工资表
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '序号' 使用智能推断类型: text_string
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '序号' (第0行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '序号' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '工号' 使用智能推断类型: employee_id_string
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '工号' (第1行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '工号' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '姓名' 使用智能推断类型: name_string
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '姓名' (第2行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '姓名' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '部门名称' 使用智能推断类型: text_string
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '部门名称' (第3行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '部门名称' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '人员类别代码' 使用智能推断类型: personnel_category_code
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '人员类别代码' (第4行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '人员类别代码' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '人员类别' 使用智能推断类型: personnel_category_code
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '人员类别' (第5行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '人员类别' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2025年岗位工资' 使用智能推断类型: salary_float
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2025年岗位工资' (第6行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2025年岗位工资' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2025年薪级工资' 使用智能推断类型: salary_float
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2025年薪级工资' (第7行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2025年薪级工资' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '津贴' 使用智能推断类型: salary_float
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '津贴' (第8行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '津贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '结余津贴' 使用智能推断类型: salary_float
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '结余津贴' (第9行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '结余津贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2025年基础性绩效' 使用智能推断类型: salary_float
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2025年基础性绩效' (第10行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2025年基础性绩效' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '卫生费' 使用智能推断类型: salary_float
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '卫生费' (第11行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '卫生费' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '交通补贴' 使用智能推断类型: salary_float
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '交通补贴' (第12行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '交通补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '物业补贴' 使用智能推断类型: salary_float
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '物业补贴' (第13行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '物业补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '住房补贴' 使用智能推断类型: salary_float
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '住房补贴' (第14行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '住房补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '车补' 使用智能推断类型: text_string
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '车补' (第15行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '车补' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '通讯补贴' 使用智能推断类型: salary_float
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '通讯补贴' (第16行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '通讯补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2025年奖励性绩效预发' 使用智能推断类型: salary_float
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2025年奖励性绩效预发' (第17行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2025年奖励性绩效预发' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '补发' 使用智能推断类型: text_string
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '补发' (第18行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '补发' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '借支' 使用智能推断类型: text_string
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '借支' (第19行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '借支' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '应发工资' 使用智能推断类型: salary_float
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '应发工资' (第20行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '应发工资' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2025公积金' 使用智能推断类型: text_string
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2025公积金' (第21行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2025公积金' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '代扣代存养老保险' 使用智能推断类型: text_string
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '代扣代存养老保险' (第22行) 设置即时保存机制
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '代扣代存养老保险' currentTextChanged -> 即时保存处理函数
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:48:57 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段映射表格创建完成: 23 行
2025-09-04 10:48:57 - src.gui.core.smart_mapping_engine - INFO - 开始生成智能映射: 类型=💰 工资表, 字段数=23
2025-09-04 10:48:57 - src.gui.core.smart_mapping_engine - INFO - 智能映射生成完成: 高置信度 4 个, 中等置信度 3 个
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 智能映射生成成功
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] Sheet '全部在职人员工资表' 字段映射已更新
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: 全部在职人员工资表
2025-09-04 10:48:59.228 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 10:48:59.229 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 10:48:59.229 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 10:48:59.334 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-09-04 10:48:59.335 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 10:48:59.336 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-09-04 10:48:59.337 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 100行 x 23列
2025-09-04 10:48:59.339 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 100行 × 23列
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 开始获取字段映射配置，表格行数: 23
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第0行字段: '序号'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '序号' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '序号', 'db_field': '序号'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第1行字段: '工号'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工号', 数据: 'employee_id_string'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '工号' 最终配置: {'field_type': 'employee_id_string', 'data_type': 'VARCHAR(100)', 'is_required': True, 'display_name': '工号', 'db_field': '工号'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第2行字段: '姓名'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '姓名', 数据: 'name_string'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '姓名' 最终配置: {'field_type': 'name_string', 'data_type': 'VARCHAR(100)', 'is_required': True, 'display_name': '姓名', 'db_field': '姓名'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第3行字段: '部门名称'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '部门名称' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '部门名称', 'db_field': '部门'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第4行字段: '人员类别代码'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '人员类别代码', 数据: 'personnel_category_code'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '人员类别代码' 最终配置: {'field_type': 'personnel_category_code', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '人员类别代码', 'db_field': '人员类别代码'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第5行字段: '人员类别'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '人员类别代码', 数据: 'personnel_category_code'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '人员类别' 最终配置: {'field_type': 'personnel_category_code', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '人员类别', 'db_field': '人员类别'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第6行字段: '2025年岗位工资'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年岗位工资' 最终配置: {'field_type': 'salary_float', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '2025年岗位工资', 'db_field': 'position'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第7行字段: '2025年薪级工资'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年薪级工资' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年薪级工资', 'db_field': 'money'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第8行字段: '津贴'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '津贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '津贴', 'db_field': '津贴'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第9行字段: '结余津贴'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '结余津贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '结余津贴', 'db_field': 'money'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第10行字段: '2025年基础性绩效'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年基础性绩效' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年基础性绩效', 'db_field': 'money'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第11行字段: '卫生费'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '卫生费' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '卫生费', 'db_field': '卫生费'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第12行字段: '交通补贴'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '交通补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '交通补贴', 'db_field': 'money'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第13行字段: '物业补贴'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '物业补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '物业补贴', 'db_field': 'money'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第14行字段: '住房补贴'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '住房补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '住房补贴', 'db_field': 'money'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第15行字段: '车补'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '车补' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '车补', 'db_field': '车补'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第16行字段: '通讯补贴'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '通讯补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '通讯补贴', 'db_field': 'money'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第17行字段: '2025年奖励性绩效预发'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025年奖励性绩效预发' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025年奖励性绩效预发', 'db_field': 'money'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第18行字段: '补发'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '补发' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '补发', 'db_field': '补发'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第19行字段: '借支'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '借支' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '借支', 'db_field': '借支'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第20行字段: '应发工资'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '应发工资' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': True, 'display_name': '应发工资', 'db_field': 'money'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第21行字段: '2025公积金'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2025公积金' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '2025公积金', 'db_field': '2025公积金'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第22行字段: '代扣代存养老保险'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '代扣代存养老保险' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '代扣代存养老保险', 'db_field': 'money'}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 获取字段映射配置完成，共 23 个字段
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [关键修复] 获取到 23 个字段映射配置
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 开始显示预览数据: 全部在职人员工资表, 数据行数: 100
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段映射配置: {'序号': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '工号': {'field_type': 'employee_id_string', 'data_type': 'VARCHAR(100)', 'is_required': True}, '姓名': {'field_type': 'name_string', 'data_type': 'VARCHAR(100)', 'is_required': True}, '部门名称': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '人员类别代码': {'field_type': 'personnel_category_code', 'data_type': 'VARCHAR(100)', 'is_required': False}, '人员类别': {'field_type': 'personnel_category_code', 'data_type': 'VARCHAR(100)', 'is_required': False}, '2025年岗位工资': {'field_type': 'salary_float', 'data_type': 'VARCHAR(100)', 'is_required': False}, '2025年薪级工资': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '津贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '结余津贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '2025年基础性绩效': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '卫生费': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '交通补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '物业补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '住房补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '车补': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '通讯补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '2025年奖励性绩效预发': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '补发': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '借支': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '应发工资': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': True}, '2025公积金': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '代扣代存养老保险': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}}
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 获取格式化引擎成功
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 设置表格结构: 23 列, 100 行
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '序号' 映射到类型: text_string
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '工号' 映射到类型: employee_id_string
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '姓名' 映射到类型: name_string
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '部门名称' 映射到类型: text_string
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员类别代码' 映射到类型: personnel_category_code
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员类别代码' 格式化: '1.0' -> '01'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员类别' 映射到类型: personnel_category_code
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年岗位工资' 映射到类型: salary_float
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年岗位工资' 格式化: '2880' -> '2,880.00'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年薪级工资' 映射到类型: salary_float
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年薪级工资' 格式化: '2375' -> '2,375.00'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '津贴' 映射到类型: salary_float
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '津贴' 格式化: '102.0' -> '102.00'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '结余津贴' 映射到类型: salary_float
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '结余津贴' 格式化: '56' -> '56.00'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年基础性绩效' 映射到类型: salary_float
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年基础性绩效' 格式化: '3594' -> '3,594.00'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '卫生费' 映射到类型: salary_float
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '卫生费' 格式化: '0.0' -> '0.00'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '交通补贴' 映射到类型: salary_float
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '交通补贴' 格式化: '220' -> '220.00'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '物业补贴' 映射到类型: salary_float
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '物业补贴' 格式化: '240' -> '240.00'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '住房补贴' 映射到类型: salary_float
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '住房补贴' 格式化: '271.9745083294993' -> '271.97'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '车补' 映射到类型: text_string
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '通讯补贴' 映射到类型: salary_float
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '通讯补贴' 格式化: '50.0' -> '50.00'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年奖励性绩效预发' 映射到类型: salary_float
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025年奖励性绩效预发' 格式化: '2500' -> '2,500.00'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '补发' 映射到类型: text_string
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '借支' 映射到类型: text_string
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '应发工资' 映射到类型: salary_float
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '应发工资' 格式化: '12288.9745083295' -> '12,288.97'
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2025公积金' 映射到类型: text_string
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '代扣代存养老保险' 映射到类型: text_string
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: 全部在职人员工资表 - 100 行
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 100 行, 23 列
2025-09-04 10:48:59 - src.gui.unified_data_import_window - INFO - 已加载Sheet '全部在职人员工资表' 的预览数据: 100 行
2025-09-04 10:49:01 - src.gui.unified_data_import_window - INFO - 选中Sheet: 退休人员工资表
2025-09-04 10:49:01.633 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '退休人员工资表' 创建默认配置
2025-09-04 10:49:01 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 当前Sheet变化: 退休人员工资表
2025-09-04 10:49:01 - src.gui.unified_data_import_window - WARNING - 🔧 [方案B] mapping_tab没有_smart_save_user_modified_configs方法
2025-09-04 10:49:01 - src.gui.unified_data_import_window - WARNING - 🔧 [方案B] 智能保存失败，尝试强制保存
2025-09-04 10:49:01 - src.gui.unified_data_import_window - INFO - 🚨 [强制保存] 开始批量保存所有字段的当前配置状态
2025-09-04 10:49:01 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:49:01 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:49:01.726 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1054 | 🔧 [方案B] 用户修改配置已保存: mapping_config_default_sheet.序号 (版本: v1756954141)
2025-09-04 10:49:01.836 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.工号 (版本: v1756954141)
2025-09-04 10:49:01.930 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.姓名 (版本: v1756954141)
2025-09-04 10:49:02.040 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.部门名称 (版本: v1756954141)
2025-09-04 10:49:02.132 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.人员类别代码 (版本: v1756954142)
2025-09-04 10:49:02.226 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.人员类别 (版本: v1756954142)
2025-09-04 10:49:02.320 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.2025年岗位工资 (版本: v1756954142)
2025-09-04 10:49:02.414 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.2025年薪级工资 (版本: v1756954142)
2025-09-04 10:49:02.523 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.津贴 (版本: v1756954142)
2025-09-04 10:49:02.632 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.结余津贴 (版本: v1756954142)
2025-09-04 10:49:02.711 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.2025年基础性绩效 (版本: v1756954142)
2025-09-04 10:49:02.820 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.卫生费 (版本: v1756954142)
2025-09-04 10:49:02.914 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.交通补贴 (版本: v1756954142)
2025-09-04 10:49:03.039 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.物业补贴 (版本: v1756954142)
2025-09-04 10:49:03.133 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.住房补贴 (版本: v1756954143)
2025-09-04 10:49:03.227 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.车补 (版本: v1756954143)
2025-09-04 10:49:03.336 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.通讯补贴 (版本: v1756954143)
2025-09-04 10:49:03.445 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.2025年奖励性绩效预发 (版本: v1756954143)
2025-09-04 10:49:03.539 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.补发 (版本: v1756954143)
2025-09-04 10:49:03.648 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.借支 (版本: v1756954143)
2025-09-04 10:49:03.742 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.应发工资 (版本: v1756954143)
2025-09-04 10:49:03.851 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.2025公积金 (版本: v1756954143)
2025-09-04 10:49:03.961 | INFO     | src.modules.data_import.config_sync_manager:save_field_mapping:1056 | 🔧 [方案B] 智能推断配置已保存: mapping_config_default_sheet.代扣代存养老保险 (版本: v1756954143)
2025-09-04 10:49:03 - src.gui.unified_data_import_window - INFO - 🚨 [强制保存] 完成，成功保存 23/23 个字段配置
2025-09-04 10:49:03 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 已强制保存当前Sheet的所有字段配置
2025-09-04 10:49:03.961 | INFO     | src.modules.data_import.formatting_engine:clear_temporary_field_types:866 | 🔧 [方案一实施] 已清理 0 个临时字段类型
2025-09-04 10:49:03 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 已清理临时字段类型
2025-09-04 10:49:03.961 | INFO     | src.modules.data_import.sheet_config_manager:get_or_create_config:161 | 为Sheet '退休人员工资表' 创建默认配置
2025-09-04 10:49:03.961 | INFO     | src.gui.widgets.data_processing_widget:_load_template_list:732 | 加载了 10 个模板
2025-09-04 10:49:03.961 | INFO     | src.gui.widgets.data_processing_widget:update_for_sheet:416 | 数据处理配置已切换到Sheet: 退休人员工资表
2025-09-04 10:49:03.961 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 10:49:03.961 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 10:49:03.961 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 10:49:04.054 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 27列 (列过滤: 否)
2025-09-04 10:49:04.054 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 10:49:04.070 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-09-04 10:49:04.070 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 1行 x 27列
2025-09-04 10:49:04.070 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 1行 × 27列
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 为Sheet '退休人员工资表' 获取到 27 个字段
2025-09-04 10:49:04 - src.gui.unified_data_import_window - WARNING - 💾 [Sheet名称] 无法获取当前Sheet，使用默认值
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [表名生成] 备用稳定表名: mapping_config_default_sheet
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 加载Sheet '退休人员工资表' 配置，表名: mapping_config_default_sheet
2025-09-04 10:49:04.070 | INFO     | src.modules.data_import.config_sync_manager:_extract_field_configs:538 |  [配置提取] 加载完整字段配置: mapping_config_default_sheet, 字段数: 32
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案B] 找到 32 个已保存的字段配置
2025-09-04 10:49:04 - src.gui.unified_data_import_window - ERROR - 🔧 [方案B] 加载Sheet配置失败: 'UnifiedMappingConfigWidget' object has no attribute '_enhance_configs_with_version_control'
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 加载Excel字段: 27 个字段, 表类型: 💰 工资表
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '序号' 使用智能推断类型: text_string
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '序号' (第0行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '序号' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '人员代码' 使用智能推断类型: code_string
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '人员代码' (第1行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '人员代码' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '姓名' 使用智能推断类型: name_string
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '姓名' (第2行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '姓名' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '部门名称' 使用智能推断类型: text_string
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '部门名称' (第3行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '部门名称' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '人员类别代码' 使用智能推断类型: personnel_category_code
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '人员类别代码' (第4行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '人员类别代码' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '基本退休费' 使用智能推断类型: salary_float
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '基本退休费' (第5行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '基本退休费' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '津贴' 使用智能推断类型: salary_float
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '津贴' (第6行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '津贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '结余津贴' 使用智能推断类型: salary_float
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '结余津贴' (第7行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '结余津贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '离退休生活补贴' 使用智能推断类型: salary_float
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '离退休生活补贴' (第8行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '离退休生活补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '护理费' 使用智能推断类型: salary_float
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '护理费' (第9行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '护理费' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '物业补贴' 使用智能推断类型: salary_float
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '物业补贴' (第10行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '物业补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '住房补贴' 使用智能推断类型: salary_float
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '住房补贴' (第11行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '住房补贴' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '增资预付' 使用智能推断类型: text_string
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '增资预付' (第12行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '增资预付' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2016待遇调整' 使用智能推断类型: text_string
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2016待遇调整' (第13行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2016待遇调整' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2017待遇调整' 使用智能推断类型: text_string
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2017待遇调整' (第14行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2017待遇调整' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2018待遇调整' 使用智能推断类型: text_string
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2018待遇调整' (第15行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2018待遇调整' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2019待遇调整' 使用智能推断类型: text_string
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2019待遇调整' (第16行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2019待遇调整' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2020待遇调整' 使用智能推断类型: text_string
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2020待遇调整' (第17行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2020待遇调整' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2021待遇调整' 使用智能推断类型: text_string
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2021待遇调整' (第18行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2021待遇调整' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2022待遇调整' 使用智能推断类型: text_string
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2022待遇调整' (第19行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2022待遇调整' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '2023待遇调整' 使用智能推断类型: text_string
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '2023待遇调整' (第20行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '2023待遇调整' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '补发' 使用智能推断类型: text_string
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '补发' (第21行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '补发' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '借支' 使用智能推断类型: text_string
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '借支' (第22行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '借支' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '应发工资' 使用智能推断类型: salary_float
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '应发工资' (第23行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '应发工资' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '公积' 使用智能推断类型: text_string
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '公积' (第24行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '公积' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '保险扣款' 使用智能推断类型: text_string
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '保险扣款' (第25行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '保险扣款' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段 '备注' 使用智能推断类型: text_string
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案一实施] 获取内置字段类型: 10 个
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 开始为字段 '备注' (第26行) 设置即时保存机制
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] ✅ 信号连接成功: '备注' currentTextChanged -> 即时保存处理函数
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 💾 [即时保存] 信号对象验证: <class 'PyQt5.QtCore.pyqtBoundSignal'>
2025-09-04 10:49:04 - src.gui.unified_data_import_window - INFO - 🔧 [方案A实施] 字段映射表格创建完成: 27 行
2025-09-04 10:49:04 - src.gui.core.smart_mapping_engine - INFO - 开始生成智能映射: 类型=💰 工资表, 字段数=27
2025-09-04 10:49:04 - src.gui.core.smart_mapping_engine - INFO - 智能映射生成完成: 高置信度 4 个, 中等置信度 9 个
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 智能映射生成成功
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] Sheet '退休人员工资表' 字段映射已更新
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 更新预览验证内容: 退休人员工资表
2025-09-04 10:49:06.276 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-09-04 10:49:06.277 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-09-04 10:49:06.277 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-09-04 10:49:06.381 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 27列 (列过滤: 否)
2025-09-04 10:49:06.383 | INFO     | src.modules.data_import.excel_importer:_clean_data:602 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-09-04 10:49:06.384 | INFO     | src.modules.data_import.excel_importer:_clean_data:613 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-09-04 10:49:06.385 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 14行 x 27列
2025-09-04 10:49:06.387 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:742 | 数据质量检查: 已过滤1条姓名为空的记录
2025-09-04 10:49:06.388 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:785 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-09-04 10:49:06.388 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 13行 × 27列
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 开始获取字段映射配置，表格行数: 27
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第0行字段: '序号'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '序号' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '序号', 'db_field': '序号'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第1行字段: '人员代码'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '代码', 数据: 'code_string'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '人员代码' 最终配置: {'field_type': 'code_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '人员代码', 'db_field': '人员代码'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第2行字段: '姓名'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '姓名', 数据: 'name_string'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '姓名' 最终配置: {'field_type': 'name_string', 'data_type': 'VARCHAR(100)', 'is_required': True, 'display_name': '姓名', 'db_field': '姓名'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第3行字段: '部门名称'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '部门名称' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '部门名称', 'db_field': '部门'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第4行字段: '人员类别代码'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '人员类别代码', 数据: 'personnel_category_code'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '人员类别代码' 最终配置: {'field_type': 'personnel_category_code', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '人员类别代码', 'db_field': '人员类别代码'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第5行字段: '基本退休费'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '基本退休费' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '基本退休费', 'db_field': '基本退休费'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第6行字段: '津贴'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '津贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '津贴', 'db_field': '津贴'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第7行字段: '结余津贴'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '结余津贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '结余津贴', 'db_field': 'money'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第8行字段: '离退休生活补贴'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '离退休生活补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '离退休生活补贴', 'db_field': 'money'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第9行字段: '护理费'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '护理费' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '护理费', 'db_field': '护理费'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第10行字段: '物业补贴'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '物业补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '物业补贴', 'db_field': 'money'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第11行字段: '住房补贴'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '住房补贴' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '住房补贴', 'db_field': 'money'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第12行字段: '增资预付'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '增资预付' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '增资预付', 'db_field': '增资预付'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第13行字段: '2016待遇调整'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2016待遇调整' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '2016待遇调整', 'db_field': 'change'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第14行字段: '2017待遇调整'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2017待遇调整' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '2017待遇调整', 'db_field': 'change'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第15行字段: '2018待遇调整'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2018待遇调整' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '2018待遇调整', 'db_field': 'change'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第16行字段: '2019待遇调整'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2019待遇调整' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '2019待遇调整', 'db_field': 'change'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第17行字段: '2020待遇调整'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2020待遇调整' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '2020待遇调整', 'db_field': 'change'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第18行字段: '2021待遇调整'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2021待遇调整' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '2021待遇调整', 'db_field': 'change'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第19行字段: '2022待遇调整'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2022待遇调整' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '2022待遇调整', 'db_field': 'change'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第20行字段: '2023待遇调整'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '2023待遇调整' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '2023待遇调整', 'db_field': 'change'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第21行字段: '补发'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '补发' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '补发', 'db_field': '补发'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第22行字段: '借支'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '借支' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '借支', 'db_field': '借支'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第23行字段: '应发工资'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '工资金额', 数据: 'salary_float'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '应发工资' 最终配置: {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': True, 'display_name': '应发工资', 'db_field': 'money'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第24行字段: '公积'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '公积' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '公积', 'db_field': '公积'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第25行字段: '保险扣款'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '保险扣款' 最终配置: {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False, 'display_name': '保险扣款', 'db_field': '扣款'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 处理第26行字段: '备注'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段类型下拉框 - 显示: '文本字符串', 数据: 'text_string'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段 '备注' 最终配置: {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False, 'display_name': '备注', 'db_field': '备注'}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 获取字段映射配置完成，共 27 个字段
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [关键修复] 获取到 27 个字段映射配置
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 开始显示预览数据: 退休人员工资表, 数据行数: 13
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 字段映射配置: {'序号': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '人员代码': {'field_type': 'code_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '姓名': {'field_type': 'name_string', 'data_type': 'VARCHAR(100)', 'is_required': True}, '部门名称': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '人员类别代码': {'field_type': 'personnel_category_code', 'data_type': 'VARCHAR(100)', 'is_required': False}, '基本退休费': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '津贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '结余津贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '离退休生活补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '护理费': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '物业补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '住房补贴': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '增资预付': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '2016待遇调整': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '2017待遇调整': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '2018待遇调整': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '2019待遇调整': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '2020待遇调整': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '2021待遇调整': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '2022待遇调整': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '2023待遇调整': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '补发': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '借支': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '应发工资': {'field_type': 'salary_float', 'data_type': 'DECIMAL(10,2)', 'is_required': True}, '公积': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}, '保险扣款': {'field_type': 'text_string', 'data_type': 'DECIMAL(10,2)', 'is_required': False}, '备注': {'field_type': 'text_string', 'data_type': 'VARCHAR(100)', 'is_required': False}}
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 获取格式化引擎成功
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [调试] 设置表格结构: 27 列, 13 行
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '序号' 映射到类型: text_string
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员代码' 映射到类型: code_string
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员代码' 格式化: '19709165.0' -> '19709165'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '姓名' 映射到类型: name_string
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '部门名称' 映射到类型: text_string
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员类别代码' 映射到类型: personnel_category_code
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '人员类别代码' 格式化: '5.0' -> '05'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '基本退休费' 映射到类型: salary_float
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '基本退休费' 格式化: '1146.9' -> '1,146.90'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '津贴' 映射到类型: salary_float
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '津贴' 格式化: '0.0' -> '0.00'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '结余津贴' 映射到类型: salary_float
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '结余津贴' 格式化: '56.0' -> '56.00'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '离退休生活补贴' 映射到类型: salary_float
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '离退休生活补贴' 格式化: '992.0' -> '992.00'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '护理费' 映射到类型: salary_float
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '物业补贴' 映射到类型: salary_float
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '物业补贴' 格式化: '160.0' -> '160.00'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '住房补贴' 映射到类型: salary_float
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '住房补贴' 格式化: '47.0' -> '47.00'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '增资预付' 映射到类型: text_string
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2016待遇调整' 映射到类型: text_string
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2017待遇调整' 映射到类型: text_string
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2018待遇调整' 映射到类型: text_string
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2019待遇调整' 映射到类型: text_string
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2020待遇调整' 映射到类型: text_string
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2021待遇调整' 映射到类型: text_string
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2022待遇调整' 映射到类型: text_string
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '2023待遇调整' 映射到类型: text_string
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '补发' 映射到类型: text_string
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '借支' 映射到类型: text_string
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '应发工资' 映射到类型: salary_float
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '应发工资' 格式化: '3360.3400000000006' -> '3,360.34'
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '公积' 映射到类型: text_string
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '保险扣款' 映射到类型: text_string
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 🔧 [修复] 字段 '备注' 映射到类型: text_string
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 预览数据加载完成（已应用格式化）: 退休人员工资表 - 13 行
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 成功更新预览数据: 13 行, 27 列
2025-09-04 10:49:06 - src.gui.unified_data_import_window - INFO - 已加载Sheet '退休人员工资表' 的预览数据: 13 行
2025-09-04 10:49:12.847 | INFO     | src.gui.prototype.prototype_main_window:_show_unified_import_dialog:6009 | 用户取消了数据导入
2025-09-04 10:49:14.538 | INFO     | __main__:main:519 | 应用程序正常退出
