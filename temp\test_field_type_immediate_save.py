#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字段类型即时保存机制测试脚本

测试内容：
1. 字段类型修改后的即时保存
2. Sheet切换时的配置保存确认
3. 配置冲突检测和解决
4. 配置回滚机制

使用方法：
python temp/test_field_type_immediate_save.py
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_field_type_immediate_save():
    """测试字段类型即时保存机制"""
    print("🧪 开始测试字段类型即时保存机制...")
    
    try:
        # 导入必要的模块
        from src.gui.unified_data_import_window import UnifiedMappingConfigWidget
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        # 创建QApplication实例
        app = QApplication(sys.argv)
        
        # 创建测试用的映射配置组件
        mapping_widget = UnifiedMappingConfigWidget()
        
        print("✅ 成功创建UnifiedMappingConfigWidget实例")
        
        # 测试辅助方法
        print("\n🔧 测试辅助方法...")
        
        # 测试时间戳生成
        timestamp = mapping_widget._get_current_timestamp()
        print(f"✅ 时间戳生成: {timestamp}")
        
        # 测试内存配置更新
        print("\n🔧 测试内存配置更新...")
        mapping_widget.mapping_config = {}  # 初始化空配置
        
        # 模拟更新字段类型
        success = mapping_widget._update_field_type_in_memory(0, "salary_float")
        print(f"✅ 内存配置更新: {success}")
        print(f"   当前配置: {mapping_widget.mapping_config}")
        
        # 测试字段保存状态标记
        print("\n🔧 测试字段保存状态标记...")
        mapping_widget._mark_field_saved(0)
        print(f"✅ 字段保存状态标记完成")
        print(f"   标记后配置: {mapping_widget.mapping_config}")
        
        # 测试字段类型回滚
        print("\n🔧 测试字段类型回滚...")
        # 先更新为另一个类型
        mapping_widget._update_field_type_in_memory(0, "employee_id_string")
        print(f"   更新后配置: {mapping_widget.mapping_config}")
        
        # 然后回滚
        mapping_widget._rollback_field_type(0)
        print(f"✅ 字段类型回滚完成")
        print(f"   回滚后配置: {mapping_widget.mapping_config}")
        
        # 测试配置冲突检测
        print("\n🔧 测试配置冲突检测...")
        
        # 模拟保存的配置
        saved_config = {
            "工号": {
                "field_type": "employee_id_string",
                "last_modified": time.time() + 10  # 比当前时间新
            }
        }
        
        # 模拟当前UI状态
        mapping_widget.mapping_config = {
            "工号": {
                "field_type": "salary_float",
                "last_modified": time.time()
            }
        }
        
        # 测试冲突解决
        conflict_resolved = mapping_widget._resolve_config_conflicts(saved_config)
        print(f"✅ 配置冲突检测: {conflict_resolved}")
        print(f"   冲突解决结果: {saved_config}")
        
        print("\n🎉 所有测试完成！")
        
        # 清理
        app.quit()
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保在项目根目录下运行此脚本")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_config_sync_manager():
    """测试配置同步管理器"""
    print("\n🧪 测试配置同步管理器...")
    
    try:
        from src.modules.data_import.config_sync_manager import ConfigSyncManager
        
        # 创建配置同步管理器实例
        config_manager = ConfigSyncManager()
        print("✅ 成功创建ConfigSyncManager实例")
        
        # 测试基本功能
        print(f"   配置目录: {config_manager.config_dir}")
        print(f"   是否初始化: {hasattr(config_manager, 'field_mapping_manager')}")
        
    except ImportError as e:
        print(f"❌ 导入ConfigSyncManager失败: {e}")
    except Exception as e:
        print(f"❌ 测试ConfigSyncManager失败: {e}")

def main():
    """主函数"""
    print("🚀 字段类型即时保存机制测试")
    print("=" * 50)
    
    # 测试字段类型即时保存
    test_field_type_immediate_save()
    
    # 测试配置同步管理器
    test_config_sync_manager()
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
