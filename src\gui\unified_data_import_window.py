"""
统一数据导入窗口
融合旧窗口成熟业务逻辑与新窗口现代化UI设计
"""

import sys
import logging
import json
import os
import time
from typing import Dict, List, Optional, Any
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QSplitter,
    QWidget, QLabel, QPushButton, QComboBox, QFileDialog,
    QProgressBar, QStatusBar, QTabWidget, QTreeWidget,
    QTableWidget, QTableWidgetItem, QToolBar, QFrame, QGroupBox,
    QScrollArea, QLineEdit, QCheckBox, QRadioButton, QButtonGroup,
    QTextEdit, QSpinBox, QDoubleSpinBox, QSlider, QListWidget,
    QListWidgetItem, QMessageBox, QApplication, QSizePolicy,
    QHeaderView, QTreeWidgetItem
)
from PyQt5.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtWidgets import QProgressDialog

# 导入现有的核心业务组件
from src.modules.data_import.multi_sheet_importer import MultiSheetImporter
from src.modules.data_import.excel_importer import ExcelImporter
from src.modules.data_import.data_validator import DataValidator
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import ConfigManager
from src.modules.system_config.user_preferences import UserPreferences
from src.modules.logging.setup_logger import setup_logger
from src.gui.workers.data_import_worker import DataImportWorker


class UnifiedDataImportWindow(QDialog):
    """统一数据导入窗口主类"""

    # 信号定义
    import_completed = pyqtSignal(bool, str)  # 导入完成信号(成功, 消息)
    status_updated = pyqtSignal(str)  # 状态更新信号
    progress_updated = pyqtSignal(int)  # 进度更新信号

    def __init__(self, parent=None, db_manager=None, config_manager=None):
        super().__init__(parent)

        # 设置窗口属性
        self.setWindowTitle("统一数据导入配置")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)

        # 添加窗口控制按钮
        self.setWindowFlags(
            Qt.Window |
            Qt.WindowMaximizeButtonHint |
            Qt.WindowMinimizeButtonHint |
            Qt.WindowCloseButtonHint
        )

        # 保存依赖注入的管理器
        self.db_manager = db_manager
        self.config_manager = config_manager

        # 初始化日志
        self.logger = setup_logger(__name__)
        self.logger.info("初始化统一数据导入窗口")

        # 初始化核心组件
        self._init_core_components()

        # 初始化变量
        self._init_variables()

        # 初始化工作线程
        self._init_worker_thread()

        # 创建UI
        self._init_ui()

        # 连接信号
        self._connect_signals()

        # 初始化响应式布局（延迟执行确保UI完全创建）
        QTimer.singleShot(50, self._initialize_responsive_layout)

        self.logger.info("统一数据导入窗口初始化完成")

    def _initialize_responsive_layout(self):
        """初始化响应式布局"""
        try:
            # 确保分割器尺寸正确设置
            self._setup_responsive_splitter()

            # 确保表格列宽正确设置
            if hasattr(self, 'mapping_tab') and hasattr(self.mapping_tab, 'mapping_table'):
                self.mapping_tab._setup_table_responsive_columns(self.mapping_tab.mapping_table)

            # 确保Sheet树列宽正确设置
            if hasattr(self, 'sheet_management_widget') and hasattr(self.sheet_management_widget, 'sheet_tree'):
                self.sheet_management_widget._setup_sheet_tree_responsive_columns(self.sheet_management_widget.sheet_tree)

            # 根据窗口大小优化组件间距
            self._optimize_component_spacing()

            self.logger.info("响应式布局初始化完成")

        except Exception as e:
            self.logger.warning(f"响应式布局初始化失败: {e}")

    def _optimize_component_spacing(self):
        """根据窗口大小优化组件间距"""
        try:
            window_width = self.width()
            window_height = self.height()

            # 根据窗口大小调整间距策略
            if window_width < 1000:
                # 小窗口：紧凑布局
                spacing_factor = 0.7
                margin_factor = 0.8
                self.logger.debug("应用紧凑布局（小窗口）")
            elif window_width > 1600:
                # 大窗口：宽松布局
                spacing_factor = 1.3
                margin_factor = 1.2
                self.logger.debug("应用宽松布局（大窗口）")
            else:
                # 标准窗口：默认布局
                spacing_factor = 1.0
                margin_factor = 1.0
                self.logger.debug("应用标准布局（中等窗口）")

            # 应用动态间距到主要布局容器
            if hasattr(self, 'main_content'):
                # 这里可以根据需要调整主要内容区域的间距
                pass

            # 记录当前的布局优化状态
            self._current_layout_state = {
                'window_size': (window_width, window_height),
                'spacing_factor': spacing_factor,
                'margin_factor': margin_factor
            }

        except Exception as e:
            self.logger.warning(f"组件间距优化失败: {e}")

    def resizeEvent(self, event):
        """窗口大小变化事件处理"""
        super().resizeEvent(event)

        # 延迟更新分割器尺寸，避免频繁调整
        if hasattr(self, '_resize_timer'):
            self._resize_timer.stop()
        else:
            from PyQt5.QtCore import QTimer
            self._resize_timer = QTimer()
            self._resize_timer.setSingleShot(True)
            self._resize_timer.timeout.connect(self._update_layout_on_resize)

        # 100ms延迟更新，避免拖动时频繁刷新
        self._resize_timer.start(100)

    def _update_layout_on_resize(self):
        """响应窗口大小变化更新布局"""
        try:
            # 更新分割器尺寸
            self._update_splitter_sizes()

            # 更新表格列宽
            if hasattr(self, 'mapping_tab') and hasattr(self.mapping_tab, 'mapping_table'):
                self._update_table_column_widths()

            # 更新Sheet树列宽
            if hasattr(self, 'sheet_management_widget') and hasattr(self.sheet_management_widget, 'sheet_tree'):
                self._update_sheet_tree_column_widths()

            self.logger.debug(f"布局已更新，窗口尺寸: {self.width()}x{self.height()}")

        except Exception as e:
            self.logger.warning(f"布局更新失败: {e}")

    def _update_sheet_tree_column_widths(self):
        """更新Sheet树列宽（响应式）"""
        try:
            tree = self.sheet_management_widget.sheet_tree
            if not tree or tree.columnCount() == 0:
                return

            # 获取左侧面板的实际宽度
            panel_width = 250  # 默认值
            if hasattr(self, 'main_splitter') and self.main_splitter.sizes():
                panel_width = self.main_splitter.sizes()[0]

            # 预留边距和滚动条宽度
            available_width = max(panel_width - 30, 200)

            # 定义各列的相对宽度比例
            column_ratios = [0.50, 0.20, 0.20, 0.10]  # Sheet名称、状态、记录数、操作
            min_widths = [80, 40, 50, 30]

            # 计算并设置列宽
            for col, (ratio, min_width) in enumerate(zip(column_ratios, min_widths)):
                if col < tree.columnCount():
                    calculated_width = int(available_width * ratio)
                    final_width = max(calculated_width, min_width)
                    tree.setColumnWidth(col, final_width)

            self.logger.debug(f"Sheet树列宽已更新，面板宽度: {panel_width}px")

        except Exception as e:
            self.logger.warning(f"Sheet树列宽更新失败: {e}")

    def _update_table_column_widths(self):
        """更新表格列宽（响应式）"""
        try:
            table = self.mapping_tab.mapping_table
            if not table or table.columnCount() == 0:
                return

            # 获取表格可用宽度（减去边距和滚动条宽度）
            available_width = table.viewport().width() - 20  # 预留20px边距

            if available_width < 400:  # 最小宽度限制
                available_width = 400

            # 定义各列的相对宽度比例
            column_ratios = [0.25, 0.25, 0.20, 0.15, 0.10, 0.05]  # 总和为1.0

            # 计算并设置列宽
            for col, ratio in enumerate(column_ratios):
                if col < table.columnCount():
                    column_width = int(available_width * ratio)
                    table.setColumnWidth(col, max(column_width, 60))  # 最小60px

            self.logger.debug(f"表格列宽已更新，可用宽度: {available_width}px")

        except Exception as e:
            self.logger.warning(f"表格列宽更新失败: {e}")

    def _init_core_components(self):
        """初始化核心业务组件"""
        try:
            # 导入管理器
            self.import_manager = UnifiedImportManager()

            # 配置管理器
            self.config_manager = ConfigManager()

            # 用户偏好
            self.user_preferences = UserPreferences()

            self.logger.info("核心组件初始化成功")

        except Exception as e:
            self.logger.error(f"核心组件初始化失败: {e}")
            raise

    def _init_variables(self):
        # 状态跟踪
        self.current_file_path = ""
        self.current_table_type = ""
        self.import_session_active = False
        self._pending_file_path = None  # 待应用的文件路径（UI初始化前保存）
        self.mapping_configs = {}
        self.import_session_active = False

        # 🔧 [方案B实施] 初始化字段状态跟踪
        self.field_state_tracking = {}

        # 🔧 [P0修复] 加载已保存的高级配置
        self._load_saved_advanced_config()

        # 进度对话框
        self.progress_dialog = None

    def _load_saved_advanced_config(self):
        """加载已保存的高级配置"""
        try:
            config_file = "config/advanced_settings.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)

                # 应用已保存的配置（复用现有的应用逻辑）
                self._on_advanced_config_changed(saved_config)
                self.logger.info("🔧 [P0修复] 已加载保存的高级配置")
            else:
                self.logger.info("🔧 [P0修复] 未找到已保存的高级配置，使用默认设置")

        except Exception as e:
            self.logger.error(f"🔧 [P0修复] 加载高级配置失败: {e}")
            # 配置加载失败不应阻止窗口正常初始化
            import traceback
            self.logger.error(f"🔧 [P0修复] 详细错误信息: {traceback.format_exc()}")

    def _init_worker_thread(self):
        """初始化工作线程"""
        try:
            self.import_worker = DataImportWorker()

            # 连接工作线程信号
            self.import_worker.progress_updated.connect(self._on_worker_progress_updated)
            self.import_worker.status_updated.connect(self._on_worker_status_updated)
            self.import_worker.import_completed.connect(self._on_worker_import_completed)
            self.import_worker.error_occurred.connect(self._on_worker_error_occurred)

            self.logger.info("工作线程初始化完成")

        except Exception as e:
            self.logger.error(f"工作线程初始化失败: {e}")
            # 如果工作线程初始化失败，设为None，将使用同步模式
            self.import_worker = None

    def _init_ui(self):
        """初始化用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 顶部操作栏
        self.top_toolbar = self._create_top_toolbar()
        main_layout.addWidget(self.top_toolbar)

        # 主内容区域
        self.main_content = self._create_main_content_area()
        main_layout.addWidget(self.main_content, 1)  # 伸展因子为1

        # 底部操作面板
        self.bottom_panel = self._create_bottom_panel()
        main_layout.addWidget(self.bottom_panel)

        self.logger.info("UI界面创建完成")

        # UI初始化完成后，应用待处理的配置
        self._apply_pending_configurations()

    def _apply_pending_configurations(self):
        """应用UI初始化完成后的待处理配置"""
        try:
            # 应用待处理的文件路径
            if hasattr(self, '_pending_file_path') and self._pending_file_path:
                file_path = self._pending_file_path
                self.logger.info(f"应用待处理的默认文件: {file_path}")

                # 设置当前文件路径
                self.current_file_path = file_path

                # 更新UI显示
                file_name = os.path.basename(file_path)
                self.file_path_label.setText(f"文件: {file_name}")

                # 加载Excel文件的Sheet信息
                self._load_excel_sheets()

                # 发送状态更新
                self.status_updated.emit(f"✅ 已自动加载默认文件: {file_name}")

                # 清除待处理状态
                self._pending_file_path = None

                self.logger.info(f"待处理配置应用完成: {file_path}")

        except Exception as e:
            self.logger.error(f"应用待处理配置失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")

    def _create_top_toolbar(self) -> QWidget:
        """创建顶部操作栏"""
        toolbar = QFrame()
        toolbar.setFrameStyle(QFrame.StyledPanel)
        toolbar.setMinimumHeight(80)  # 增加最小高度确保内容不被遮挡
        toolbar.setMaximumHeight(100)  # 增加最大高度

        layout = QHBoxLayout(toolbar)
        layout.setContentsMargins(15, 15, 15, 15)  # 增加边距
        layout.setSpacing(20)

        # 文件选择区
        file_group = QGroupBox("文件选择")
        file_group.setMinimumHeight(60)  # 设置最小高度
        file_layout = QHBoxLayout(file_group)
        file_layout.setContentsMargins(8, 8, 8, 8)  # 增加内边距

        self.file_path_label = QLabel("未选择文件")
        self.file_path_label.setStyleSheet("color: #666; font-size: 12px; padding: 2px;")
        self.file_path_label.setWordWrap(True)  # 允许文字换行
        self.select_file_btn = QPushButton("选择Excel文件")
        self.select_file_btn.setMinimumHeight(32)  # 增加按钮高度
        self.select_file_btn.setMaximumWidth(120)  # 限制按钮宽度

        file_layout.addWidget(self.file_path_label, 1)
        file_layout.addWidget(self.select_file_btn)

        # 表类型选择区
        type_group = QGroupBox("表类型")
        type_group.setMinimumHeight(60)  # 设置最小高度
        type_layout = QHBoxLayout(type_group)
        type_layout.setContentsMargins(8, 8, 8, 8)  # 增加内边距

        self.table_type_combo = QComboBox()
        self.table_type_combo.addItems(["工资表", "异动表"])
        self.table_type_combo.setMinimumHeight(32)  # 增加下拉框高度

        type_layout.addWidget(self.table_type_combo)

        # 快速操作按钮
        action_group = QGroupBox("操作")
        action_group.setMinimumHeight(60)  # 设置最小高度
        action_layout = QHBoxLayout(action_group)
        action_layout.setContentsMargins(8, 8, 8, 8)  # 增加内边距

        self.advanced_settings_btn = QPushButton("高级设置")
        self.help_btn = QPushButton("帮助")
        self.advanced_settings_btn.setMinimumHeight(32)  # 增加按钮高度
        self.help_btn.setMinimumHeight(32)  # 增加按钮高度
        self.advanced_settings_btn.setMaximumWidth(100)  # 限制按钮宽度
        self.help_btn.setMaximumWidth(80)  # 限制按钮宽度

        # 设置工具提示
        self.select_file_btn.setToolTip("选择要导入的Excel文件")
        self.table_type_combo.setToolTip("选择数据表类型：工资表或异动表")
        self.advanced_settings_btn.setToolTip("打开系统高级配置，统一管理所有功能区域的设置选项")
        self.help_btn.setToolTip("查看使用帮助和说明")

        action_layout.addWidget(self.advanced_settings_btn)
        action_layout.addWidget(self.help_btn)

        # 添加到主布局
        layout.addWidget(file_group, 2)
        layout.addWidget(type_group, 1)
        layout.addWidget(action_group, 1)

        return toolbar

    def _create_main_content_area(self) -> QWidget:
        """创建主内容区域"""
        # 使用水平分割器
        self.main_splitter = QSplitter(Qt.Horizontal)

        # 左侧：Sheet管理面板
        self.sheet_management_widget = EnhancedSheetManagementWidget()
        self.main_splitter.addWidget(self.sheet_management_widget)

        # 右侧：配置详情面板
        self.config_details_widget = self._create_config_details_panel()
        self.main_splitter.addWidget(self.config_details_widget)

        # 设置响应式分割比例
        self._setup_responsive_splitter()

        return self.main_splitter

    def _setup_responsive_splitter(self):
        """设置响应式分割器"""
        # 获取窗口初始宽度，如果还未显示则使用默认值
        window_width = self.width() if self.width() > 100 else 1400

        # 计算动态分割比例
        # 左侧面板：最小250px，最大350px，占比20-25%
        left_min_width = 250
        left_max_width = 350
        left_ratio = 0.22  # 默认22%

        left_width = max(left_min_width, min(left_max_width, int(window_width * left_ratio)))
        right_width = window_width - left_width

        # 设置分割器尺寸
        self.main_splitter.setSizes([left_width, right_width])

        # 设置伸展因子：左侧固定，右侧可伸展
        self.main_splitter.setStretchFactor(0, 0)
        self.main_splitter.setStretchFactor(1, 1)

        # 设置分割器属性
        self.main_splitter.setCollapsible(0, False)  # 左侧面板不可完全折叠
        self.main_splitter.setCollapsible(1, False)  # 右侧面板不可完全折叠

        self.logger.info(f"响应式分割器设置完成: 左侧={left_width}px, 右侧={right_width}px")

    def _update_splitter_sizes(self):
        """更新分割器尺寸（响应窗口变化）"""
        if not hasattr(self, 'main_splitter'):
            return

        # 重新计算并应用分割比例
        self._setup_responsive_splitter()

    def _create_config_details_panel(self) -> QWidget:
        """创建右侧配置详情面板"""
        # 使用选项卡容器
        self.config_tab_widget = QTabWidget()

        # 字段映射选项卡
        self.mapping_tab = UnifiedMappingConfigWidget(
            parent=self,
            db_manager=self.db_manager,
            config_manager=self.config_manager
        )
        self.config_tab_widget.addTab(self.mapping_tab, "🔗 字段映射")

        # 数据处理选项卡
        from src.gui.widgets.data_processing_widget import DataProcessingWidget
        self.processing_tab = DataProcessingWidget()
        self.config_tab_widget.addTab(self.processing_tab, "🧹 数据处理")

        # 预览验证选项卡
        self.preview_tab = PreviewValidationWidget()
        self.config_tab_widget.addTab(self.preview_tab, "👁️ 预览验证")

        # 字段类型配置选项卡
        from src.gui.widgets.field_type_config_widget import FieldTypeConfigWidget
        self.field_types_tab = FieldTypeConfigWidget()
        self.config_tab_widget.addTab(self.field_types_tab, "📝 字段类型")

        return self.config_tab_widget

    def _create_bottom_panel(self) -> QWidget:
        """创建底部操作面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        panel.setMaximumHeight(50)

        layout = QHBoxLayout(panel)
        layout.setContentsMargins(15, 5, 15, 5)
        layout.setSpacing(10)

        # 状态信息区
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #666; font-size: 12px;")

        # 进度显示区
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)

        # 操作按钮组
        button_layout = QHBoxLayout()

        self.preview_btn = QPushButton("🔍 预览数据")
        self.save_config_btn = QPushButton("💾 保存配置")
        self.start_import_btn = QPushButton("🚀 开始导入")
        self.cancel_btn = QPushButton("❌ 取消")

        # 设置按钮样式和高度
        for btn in [self.preview_btn, self.save_config_btn, self.start_import_btn, self.cancel_btn]:
            btn.setMinimumHeight(30)
            btn.setMinimumWidth(100)

        # 重要按钮高亮
        self.start_import_btn.setStyleSheet(
            "QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }"
            "QPushButton:hover { background-color: #45a049; }"
        )

        button_layout.addWidget(self.preview_btn)
        button_layout.addWidget(self.save_config_btn)
        button_layout.addWidget(self.start_import_btn)
        button_layout.addWidget(self.cancel_btn)

        # 添加到主布局
        layout.addWidget(self.status_label, 1)
        layout.addWidget(self.progress_bar)
        layout.addLayout(button_layout)

        return panel

    def _connect_signals(self):
        """连接信号"""
        # 文件选择
        self.select_file_btn.clicked.connect(self._select_excel_file)

        # 表类型变化
        self.table_type_combo.currentTextChanged.connect(self._on_table_type_changed)

        # 顶部操作按钮
        self.advanced_settings_btn.clicked.connect(self._open_unified_advanced_settings)

        # 底部按钮
        self.preview_btn.clicked.connect(self._preview_data)
        self.save_config_btn.clicked.connect(self._save_configuration)
        self.start_import_btn.clicked.connect(self._start_import)
        self.cancel_btn.clicked.connect(self.reject)

        # Sheet管理组件信号
        self.sheet_management_widget.current_sheet_changed.connect(self._on_current_sheet_changed)
        self.sheet_management_widget.sheet_preview_requested.connect(self._on_sheet_preview_requested)
        self.sheet_management_widget.import_strategy_changed.connect(self._on_import_strategy_changed)

        # 数据处理组件信号
        self.processing_tab.config_changed.connect(self._on_processing_config_changed)
        self.processing_tab.preview_requested.connect(self._on_processing_preview_requested)

        # 状态更新
        self.status_updated.connect(self.status_label.setText)
        self.progress_updated.connect(self.progress_bar.setValue)

        # 映射配置组件信号
        if hasattr(self.mapping_tab, 'validation_completed'):
            self.mapping_tab.validation_completed.connect(self._on_mapping_validation_completed)

        # 字段类型配置组件信号连接
        if hasattr(self, 'field_types_tab'):
            # 字段类型变更时刷新映射配置中的类型选择
            self.field_types_tab.field_type_changed.connect(self._on_field_type_changed)
            # 字段类型删除时更新相关配置
            self.field_types_tab.field_type_deleted.connect(self._on_field_type_deleted)

        # 选项卡切换监听 - 修复字段映射数据丢失问题
        if hasattr(self, 'config_tab_widget'):
            self.config_tab_widget.currentChanged.connect(self._on_tab_changed)

        self.logger.info("信号连接完成")

    def _select_excel_file(self):
        """选择Excel文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择Excel文件",
            "",
            "Excel文件 (*.xlsx *.xls);;所有文件 (*)"
        )

        if file_path:
            self.current_file_path = file_path
            self.file_path_label.setText(f"文件: {file_path.split('/')[-1]}")

            # 设置Excel文件路径到Sheet配置管理器
            self.sheet_management_widget.set_excel_file(file_path)

            self._load_excel_sheets()
            self.logger.info(f"选择文件: {file_path}")

    def _on_table_type_changed(self, table_type: str):
        """表类型变化处理"""
        self.current_table_type = table_type
        self.logger.info(f"用户选择表类型: {table_type}")

        # 根据用户选择的表类型更新界面提示和行为
        if table_type == "💰 工资表":
            self.status_updated.emit("已选择工资表类型 - 将使用标准工资表模板和字段映射")

            # 工资表使用专用模板，字段相对固定
            tooltip_text = "工资表：使用标准化字段映射，支持专用模板自动识别（离休、退休、在职、A岗等）"

        elif table_type == "🔄 异动表":
            self.status_updated.emit("已选择异动表类型 - 支持灵活的字段配置和自定义映射")

            # 异动表更灵活，支持完全自定义
            tooltip_text = "异动表：支持灵活的字段配置，可完全自定义字段映射，内容结构灵活多样"

        else:
            self.status_updated.emit(f"已选择表类型: {table_type}")
            tooltip_text = f"当前选择的表类型: {table_type}"

        # 更新UI提示
        self.table_type_combo.setToolTip(tooltip_text)

        # 根据表类型重新初始化导入会话
        if self.current_file_path:
            self._initialize_import_session()

            # 如果已有Sheet信息，重新加载以应用类型特定的处理
            if hasattr(self, 'sheet_management_widget') and self.sheet_management_widget.sheet_info:
                self._update_sheet_display_for_table_type()

    def _on_tab_changed(self, index):
        """选项卡切换处理 - 修复字段映射数据丢失问题"""
        try:
            # 获取当前选项卡名称
            tab_name = self.config_tab_widget.tabText(index) if index >= 0 else "未知"
            self.logger.info(f"🔄 [修复] 选项卡切换到: {tab_name} (索引: {index})")

            # 在切换前保存当前选项卡的数据
            # 特别处理字段映射选项卡的数据保存
            if hasattr(self, 'mapping_tab') and self.mapping_tab:
                try:
                    # 使用新的保存方法确保字段映射数据已保存
                    if self.mapping_tab.save_current_config():
                        self.logger.debug("🔄 [修复] 字段映射配置已成功保存")
                    else:
                        self.logger.warning("🔄 [修复] 字段映射配置保存失败")

                except Exception as e:
                    self.logger.warning(f"🔄 [修复] 保存字段映射配置时出现警告: {e}")

            # 当切换到预览验证选项卡时，刷新预览数据并应用格式化
            if index == 2 and hasattr(self, 'preview_tab'):  # 索引2是预览验证选项卡
                try:
                    self.logger.info("🔄 [修复] 切换到预览选项卡，开始刷新格式化")
                    
                    # 🔧 [修复] 注册当前字段类型到格式化引擎
                    from src.modules.data_import.formatting_engine import get_formatting_engine
                    formatting_engine = get_formatting_engine()
                    
                    # 从映射配置中注册字段类型
                    if hasattr(self.mapping_tab, 'get_current_mapping_config'):
                        mapping_config = self.mapping_tab.get_current_mapping_config()
                        for excel_field, config in mapping_config.items():
                            field_type = config.get('field_type')
                            if field_type:
                                formatting_engine.register_field_type(excel_field, {'type': field_type})
                    
                    # 🔧 [关键修复] 检查是否有缓存的预览数据，如果没有则主动加载
                    if hasattr(self.preview_tab, 'current_preview_data') and self.preview_tab.current_preview_data:
                        self.logger.info("🔄 [修复] 使用缓存的预览数据")
                        # 使用缓存数据，重新应用格式化
                        self._refresh_preview_with_current_data()
                    else:
                        self.logger.info("🔄 [关键修复] 预览选项卡无缓存数据，主动加载当前Sheet数据")
                        # 🔧 [关键修复] 没有缓存数据时，主动加载当前选中的Sheet数据
                        current_sheet = self._get_current_selected_sheet()
                        if current_sheet:
                            self.logger.info(f"🔄 [关键修复] 加载Sheet数据: {current_sheet}")
                            self._load_and_preview_sheet_data(current_sheet)
                        else:
                            self.logger.warning("🔄 [关键修复] 没有选中的Sheet，无法加载预览数据")
                except Exception as e:
                    self.logger.warning(f"🔄 [修复] 刷新预览选项卡时出现警告: {e}")
                    import traceback
                    self.logger.warning(f"🔄 [修复] 详细错误: {traceback.format_exc()}")

            # 更新状态
            self.status_updated.emit(f"已切换到: {tab_name}")

        except Exception as e:
            self.logger.error(f"🔄 [修复] 选项卡切换处理失败: {e}")
            import traceback
            self.logger.error(f"🔄 [修复] 详细错误: {traceback.format_exc()}")

    def _refresh_preview_with_current_data(self):
        """🔧 [关键修复] 使用缓存数据刷新预览格式化"""
        try:
            # 🔧 [关键修复] 获取最新的字段映射配置
            field_mappings = {}
            if hasattr(self.mapping_tab, 'get_current_mapping_config'):
                mapping_config = self.mapping_tab.get_current_mapping_config()
                self.logger.info(f"🔄 [修复] 获取到字段映射配置: {len(mapping_config)} 个字段")
                
                # 🔧 [关键修复] 直接将字段类型注册到格式化引擎
                from src.modules.data_import.formatting_engine import get_formatting_engine
                formatting_engine = get_formatting_engine()
                
                # 转换为预览需要的格式并注册字段类型
                for excel_field, config in mapping_config.items():
                    field_type = config.get('field_type')
                    field_mappings[excel_field] = {
                        'field_type': field_type,
                        'data_type': config.get('data_type'),
                        'is_required': config.get('is_required', False)
                    }
                    
                    # 🔧 [关键修复] 直接注册字段类型到格式化引擎
                    if field_type:
                        formatting_engine.register_field_type(excel_field, {'type': field_type})
                        self.logger.debug(f"🔄 [修复] 注册字段 '{excel_field}' 类型: {field_type}")
                
                self.logger.info(f"🔄 [修复] 已注册 {len(field_mappings)} 个字段类型到格式化引擎")
            
            # 🔧 [修复] 重新显示预览数据，应用最新的格式化
            self.preview_tab.show_preview_data(
                self.preview_tab.current_sheet_name,
                self.preview_tab.current_preview_data,
                field_mappings
            )
            self.logger.info(f"🔄 [修复] 已刷新格式化 - {len(field_mappings)} 个字段映射")
            
        except Exception as e:
            self.logger.error(f"🔄 [修复] 刷新预览格式化失败: {e}")

    def _get_current_selected_sheet(self):
        """🔧 [关键修复] 获取当前选中的Sheet名称"""
        try:
            if hasattr(self, 'sheet_management_widget') and self.sheet_management_widget:
                # 尝试从Sheet管理组件获取当前选中的Sheet
                if hasattr(self.sheet_management_widget, 'get_current_sheet'):
                    return self.sheet_management_widget.get_current_sheet()
                elif hasattr(self.sheet_management_widget, 'current_sheet'):
                    return self.sheet_management_widget.current_sheet
                else:
                    # 尝试从Sheet树获取选中项
                    if hasattr(self.sheet_management_widget, 'sheet_tree'):
                        current_item = self.sheet_management_widget.sheet_tree.currentItem()
                        if current_item:
                            sheet_data = current_item.data(0, Qt.UserRole)
                            if sheet_data:
                                return sheet_data.get('name')
            
            self.logger.warning("🔄 [关键修复] 无法获取当前选中的Sheet")
            return None
            
        except Exception as e:
            self.logger.error(f"🔄 [关键修复] 获取当前Sheet失败: {e}")
            return None

    def _load_and_preview_sheet_data(self, sheet_name: str):
        """🔧 [关键修复] 加载并预览Sheet数据"""
        try:
            self.logger.info(f"🔄 [关键修复] 开始加载Sheet数据: {sheet_name}")
            
            if not self.current_file_path:
                self.logger.warning("🔄 [关键修复] 没有当前文件路径")
                return
            
            # 使用与预览按钮相同的逻辑加载数据
            df = self.import_manager.excel_importer.import_data(
                self.current_file_path, sheet_name, max_rows=10
            )

            if df is not None and not df.empty:
                # 将DataFrame转换为字典列表用于显示
                preview_data = df.to_dict('records')
                self.logger.info(f"🔄 [关键修复] 成功读取预览数据: {len(preview_data)} 行, {len(df.columns)} 列")

                # 显示预览数据（这会调用与预览按钮相同的逻辑）
                self._show_preview_data(sheet_name, preview_data)
                self.logger.info(f"🔄 [关键修复] 预览数据加载完成: {len(preview_data)} 行数据")
            else:
                self.logger.warning(f"🔄 [关键修复] 工作表 '{sheet_name}' 数据为空")
                
        except Exception as e:
            self.logger.error(f"🔄 [关键修复] 加载Sheet数据失败: {sheet_name} - {e}")
            import traceback
            self.logger.error(f"🔄 [关键修复] 详细错误: {traceback.format_exc()}")

    def _update_sheet_display_for_table_type(self):
        """根据表类型更新Sheet显示"""
        try:
            if self.current_table_type == "💰 工资表":
                # 工资表：尝试智能识别专用Sheet类型
                sheet_info = self.sheet_management_widget.sheet_info
                for sheet in sheet_info:
                    sheet_name = sheet['name'].lower()

                    # 标记专用工资表类型
                    if '离休' in sheet_name:
                        sheet['detected_type'] = 'retired_employees'
                        sheet['tooltip'] = '检测到：离休人员工资表'
                    elif '退休' in sheet_name:
                        sheet['detected_type'] = 'pension_employees'
                        sheet['tooltip'] = '检测到：退休人员工资表'
                    elif '在职' in sheet_name or '全部' in sheet_name:
                        sheet['detected_type'] = 'active_employees'
                        sheet['tooltip'] = '检测到：在职人员工资表'
                    elif 'a岗' in sheet_name or 'A岗' in sheet_name:
                        sheet['detected_type'] = 'a_grade_employees'
                        sheet['tooltip'] = '检测到：A岗职工工资表'
                    else:
                        sheet['detected_type'] = 'general_salary'
                        sheet['tooltip'] = '工资表（未识别具体类型）'

                self.status_updated.emit("已为工资表标记专用类型，将自动应用相应模板")

            elif self.current_table_type == "🔄 异动表":
                # 异动表：标记为灵活类型
                sheet_info = self.sheet_management_widget.sheet_info
                for sheet in sheet_info:
                    sheet['detected_type'] = 'change_data'
                    sheet['tooltip'] = '异动表：支持灵活字段配置'

                self.status_updated.emit("已标记为异动表，支持完全自定义的字段映射")

            # 刷新Sheet管理组件显示
            self.sheet_management_widget.load_sheets(self.sheet_management_widget.sheet_info)

        except Exception as e:
            self.logger.warning(f"更新Sheet显示失败: {e}")

    def _load_excel_sheets(self):
        """加载Excel文件的Sheet列表"""
        if not self.current_file_path:
            return

        try:
            self.status_updated.emit("正在分析Excel文件...")

            # 使用导入管理器分析文件
            sheet_info = self.import_manager.analyze_excel_file(self.current_file_path)

            # 更新Sheet管理组件
            self.sheet_management_widget.load_sheets(sheet_info)

            self.status_updated.emit(f"已加载 {len(sheet_info)} 个工作表")
            self.logger.info(f"成功加载 {len(sheet_info)} 个工作表")

        except Exception as e:
            error_msg = f"加载Excel文件失败: {e}"
            self.status_updated.emit(error_msg)
            self.logger.error(error_msg)

    def _initialize_import_session(self):
        """初始化导入会话"""
        if not self.current_file_path or not self.current_table_type:
            return

        try:
            self.status_updated.emit("正在初始化导入会话...")

            # 使用导入管理器初始化会话
            self.import_manager.initialize_import_session(
                self.current_file_path,
                self.current_table_type
            )

            self.import_session_active = True
            self.status_updated.emit("导入会话已初始化")
            self.logger.info("导入会话初始化成功")

        except Exception as e:
            error_msg = f"初始化导入会话失败: {e}"
            self.status_updated.emit(error_msg)
            self.logger.error(error_msg)

    def _preview_data(self):
        """预览数据"""
        self.logger.info("开始预览数据")

        if not self.current_file_path:
            self.status_updated.emit("请先选择Excel文件")
            return

        try:
            # 获取选中的Sheet
            selected_sheets = self.sheet_management_widget.get_selected_sheets()
            if not selected_sheets:
                self.status_updated.emit("请先选择要预览的工作表")
                return

            # 预览第一个选中的Sheet
            first_sheet = selected_sheets[0]
            sheet_name = first_sheet['name']

            self.status_updated.emit(f"正在预览工作表: {sheet_name}")

            # 使用ExcelImporter预览数据（前10行）
            preview_data = self.import_manager.excel_importer.import_data(
                self.current_file_path,
                sheet_name=sheet_name,
                max_rows=10
            )

            if preview_data is not None and len(preview_data) > 0:
                # 转换DataFrame为字典列表格式
                data_dicts = preview_data.to_dict('records')

                # 显示预览数据在预览选项卡
                self._show_preview_data(sheet_name, data_dicts)
                self.status_updated.emit(f"预览完成: {len(data_dicts)} 行数据")

                # 切换到预览选项卡
                config_details = self.config_details_widget
                if hasattr(config_details, 'setCurrentIndex'):
                    config_details.setCurrentIndex(2)  # 预览验证选项卡
            else:
                self.status_updated.emit(f"工作表 '{sheet_name}' 无数据可预览")

        except Exception as e:
            error_msg = f"预览数据失败: {e}"
            self.status_updated.emit(error_msg)
            self.logger.error(error_msg)

    def _save_configuration(self):
        """保存配置"""
        self.logger.info("保存配置")

        if not self.current_file_path:
            self.status_updated.emit("请先选择Excel文件")
            return

        try:
            # 获取当前映射配置
            mapping_config = self.mapping_tab.get_mapping_config()
            if not mapping_config:
                self.status_updated.emit("没有可保存的映射配置")
                return

            # 获取选中的Sheet配置
            selected_sheets = self.sheet_management_widget.get_selected_sheets()
            import_strategy = self.sheet_management_widget.get_import_strategy()

            # 构建完整配置
            full_config = {
                'file_path': self.current_file_path,
                'table_type': self.current_table_type,
                'selected_sheets': selected_sheets,
                'import_strategy': import_strategy,
                'mapping_config': mapping_config,
                'session_info': {
                    'created_time': self.import_manager.current_session.get('created_time') if self.import_manager.current_session else None,
                    'file_name': self.current_file_path.split('/')[-1] if self.current_file_path else None
                }
            }

            # 使用配置管理器保存配置
            config_name = f"import_config_{self.current_table_type}_{len(mapping_config)}fields"
            if self.config_manager.save_configuration(config_name, full_config):
                self.status_updated.emit(f"配置已保存: {config_name}")
                self.logger.info(f"配置保存成功: {config_name}")

                # 同时保存为模板（如果映射配置足够完整）
                if len(mapping_config) >= 3:  # 至少3个字段才保存为模板
                    try:
                        template_data = {
                            'name': f"{self.current_table_type}_自动保存模板",
                            'description': f"从{self.current_file_path.split('/')[-1]}自动生成",
                            'table_type': self.current_table_type,
                            'mapping_config': mapping_config,
                            'field_count': len(mapping_config),
                            'auto_generated': True
                        }

                        if self.mapping_tab.template_manager.save_enhanced_template(template_data):
                            self.status_updated.emit(f"配置已保存，并自动创建模板")
                    except Exception as e:
                        self.logger.warning(f"模板保存失败，但配置保存成功: {e}")
            else:
                self.status_updated.emit("配置保存失败")

        except Exception as e:
            error_msg = f"保存配置失败: {e}"
            self.status_updated.emit(error_msg)
            self.logger.error(error_msg)

    def _start_import(self):
        """开始异步导入"""
        self.logger.info("开始数据导入")

        if not self.current_file_path:
            self.status_updated.emit("请先选择Excel文件")
            return

        if not self.current_table_type:
            self.status_updated.emit("请先选择表类型")
            return

        try:
            # 获取选中的Sheet
            selected_sheets = self.sheet_management_widget.get_selected_sheets()
            if not selected_sheets:
                self.status_updated.emit("请先选择要导入的工作表")
                return

            # 确认映射配置是否完整
            mapping_config = self.mapping_tab.get_mapping_config()
            if not mapping_config:
                self.status_updated.emit("请先配置字段映射")
                return

            # 获取当前年月（从文件名推断或使用当前日期）
            import datetime
            current_date = datetime.datetime.now()
            year = current_date.year
            month = current_date.month

            # 尝试从文件名推断年月
            file_name = self.current_file_path.split('/')[-1].lower()
            if '2025' in file_name:
                year = 2025
            if '5月' in file_name or '05' in file_name:
                month = 5
            elif '8月' in file_name or '08' in file_name:
                month = 8

            # 确定target_path（这是用户决策的关键）
            if self.current_table_type == "🔄 异动表":
                target_path = "异动人员表"
            else:
                target_path = "工资表"

            # 使用异步导入
            if self.import_worker:
                self._start_async_import(year, month, target_path, selected_sheets, mapping_config)
            else:
                # 降级到同步导入
                self._start_sync_import(year, month, target_path)

        except Exception as e:
            error_msg = f"导入准备失败: {e}"
            self.status_updated.emit(error_msg)
            self.logger.error(error_msg)

    def _start_async_import(self, year: int, month: int, target_path: str,
                           selected_sheets: List, mapping_config: Dict):
        """启动异步导入"""
        # 创建进度对话框
        self.progress_dialog = QProgressDialog("正在导入数据...", "取消", 0, 100, self)
        self.progress_dialog.setWindowTitle("数据导入进度")
        self.progress_dialog.setWindowModality(Qt.WindowModal)
        self.progress_dialog.setAutoClose(False)
        self.progress_dialog.setAutoReset(False)
        self.progress_dialog.canceled.connect(self._cancel_import)

        # 配置工作线程
        self.import_worker.setup_import(
            file_path=self.current_file_path,
            year=year,
            month=month,
            target_table=None,
            target_path=target_path,
            selected_sheets=selected_sheets,
            mapping_config=mapping_config,
            multi_sheet_importer=self.import_manager.multi_sheet_importer
        )

        # 显示进度对话框并启动工作线程
        self.progress_dialog.show()
        self.import_worker.start()

        self.logger.info("异步导入已启动")

    def _start_sync_import(self, year: int, month: int, target_path: str):
        """同步导入（备用方案）"""
        self.logger.warning("使用同步导入模式")

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_updated.emit("正在执行数据导入...")

        # 调用MultiSheetImporter执行导入
        result = self.import_manager.multi_sheet_importer.import_excel_file(
            file_path=self.current_file_path,
            year=year,
            month=month,
            target_table=None,  # 使用默认命名规则
            target_path=target_path
        )

        self.progress_bar.setValue(90)

        # 处理导入结果
        if result and result.get('success', False):
            imported_tables = result.get('imported_tables', [])
            total_records = result.get('total_records', 0)

            success_msg = f"导入完成！共导入 {total_records} 条记录到 {len(imported_tables)} 个表"
            self.status_updated.emit(success_msg)
            self.logger.info(f"导入成功: {success_msg}")

            # 发送导入完成信号
            self.import_completed.emit(True, success_msg)

            # 自动保存配置
            try:
                self._save_configuration()
            except Exception as e:
                self.logger.warning(f"自动保存配置失败: {e}")

        else:
            error_msg = result.get('error', '导入失败，未知错误') if result else '导入失败，未知错误'
            self.status_updated.emit(f"导入失败: {error_msg}")
            self.logger.error(f"导入失败: {error_msg}")

            # 发送导入失败信号
            self.import_completed.emit(False, error_msg)
            self.progress_bar.setValue(100)

    def _cancel_import(self):
        """取消导入"""
        if self.import_worker and self.import_worker.isRunning():
            self.import_worker.stop_import()
            self.import_worker.wait(3000)  # 等待3秒
            if self.import_worker.isRunning():
                self.import_worker.terminate()
                self.logger.warning("强制终止导入线程")

        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None

        self.status_updated.emit("导入已取消")
        self.logger.info("用户取消了数据导入")

    def _on_worker_progress_updated(self, progress: int, status: str):
        """处理工作线程进度更新"""
        if self.progress_dialog:
            self.progress_dialog.setValue(progress)
            self.progress_dialog.setLabelText(status)

        self.progress_bar.setValue(progress)
        self.status_updated.emit(status)

    def _on_worker_status_updated(self, status: str):
        """处理工作线程状态更新"""
        self.status_updated.emit(status)

    def _on_worker_import_completed(self, success: bool, result: Dict):
        """处理工作线程导入完成"""
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None

        if success:
            imported_tables = result.get('imported_tables', [])
            total_records = result.get('total_records', 0)

            success_msg = f"导入完成！共导入 {total_records} 条记录到 {len(imported_tables)} 个表"
            self.status_updated.emit(success_msg)
            self.logger.info(f"异步导入成功: {success_msg}")

            # 发送导入完成信号
            self.import_completed.emit(True, success_msg)

            # 自动保存配置
            try:
                self._save_configuration()
            except Exception as e:
                self.logger.warning(f"自动保存配置失败: {e}")
        else:
            error_msg = result.get('error', '导入失败') if isinstance(result, dict) else str(result)
            self.status_updated.emit(f"导入失败: {error_msg}")
            self.logger.error(f"异步导入失败: {error_msg}")

            # 发送导入失败信号
            self.import_completed.emit(False, error_msg)

        self.progress_bar.setValue(100)

    def _on_worker_error_occurred(self, error_msg: str):
        """处理工作线程错误"""
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None

        self.status_updated.emit(f"错误: {error_msg}")
        self.logger.error(f"异步导入错误: {error_msg}")

        # 发送导入失败信号
        self.import_completed.emit(False, error_msg)

        self.progress_bar.setValue(0)

        # 隐藏进度条
        self.progress_bar.setVisible(False)



    def _on_sheet_preview_requested(self, sheet_name: str):
        """Sheet预览请求处理"""
        try:
            self.status_updated.emit(f"🔍 正在预览工作表: {sheet_name}")
            self.logger.info(f"开始预览Sheet: {sheet_name}")

            # 使用ExcelImporter预览数据 - 修复：处理DataFrame返回值
            df = self.import_manager.excel_importer.import_data(
                self.current_file_path, sheet_name, max_rows=10
            )

            if df is not None and not df.empty:
                # 将DataFrame转换为字典列表用于显示
                preview_data = df.to_dict('records')

                self.logger.info(f"成功读取预览数据: {len(preview_data)} 行, {len(df.columns)} 列")

                # 显示预览数据在预览选项卡
                self._show_preview_data(sheet_name, preview_data)
                self.status_updated.emit(f"✅ 预览加载完成: {len(preview_data)} 行数据")
            else:
                self.status_updated.emit(f"⚠️ 工作表 '{sheet_name}' 无数据")
                self.logger.warning(f"工作表 '{sheet_name}' 数据为空")

        except Exception as e:
            error_msg = f"❌ 预览失败: {e}"
            self.status_updated.emit(error_msg)
            self.logger.error(f"预览Sheet失败: {sheet_name} - {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")

    def _on_import_strategy_changed(self, strategy: str):
        """导入策略变化处理"""
        self.logger.info(f"导入策略变更: {strategy}")

        if strategy == "merge_to_single_table":
            self.status_updated.emit("策略: 合并到单表")
        else:
            self.status_updated.emit("策略: 分别创建表")



    def _on_mapping_validation_completed(self, is_valid: bool):
        """映射验证完成处理"""
        if is_valid:
            self.start_import_btn.setEnabled(True)
            self.start_import_btn.setStyleSheet(
                "QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }"
                "QPushButton:hover { background-color: #45a049; }"
            )
        else:
            self.start_import_btn.setEnabled(False)
            self.start_import_btn.setStyleSheet(
                "QPushButton { background-color: #ccc; color: #666; }"
            )

    def _load_sheet_headers(self, sheet_name: str):
        """加载Sheet的字段头到映射配置"""
        try:
            self.status_updated.emit(f"正在加载字段: {sheet_name}")
            self.logger.info(f"开始加载Sheet字段: {sheet_name}")

            # 获取Sheet的字段头 - 修复：使用正确的DataFrame方式
            df = self.import_manager.excel_importer.import_data(
                self.current_file_path, sheet_name, max_rows=1
            )

            if df is not None and not df.empty:
                # DataFrame的列名就是字段头
                headers = list(df.columns)

                self.logger.info(f"成功读取字段头: {headers}")

                # 加载到映射配置组件
                self.mapping_tab.load_excel_headers(headers, self.current_table_type)

                self.status_updated.emit(f"✅ 已加载 {len(headers)} 个字段")
                self.logger.info(f"成功加载字段: {sheet_name} - {len(headers)} 个字段")
            else:
                self.status_updated.emit(f"⚠️ 工作表 '{sheet_name}' 无字段信息")
                self.logger.warning(f"工作表 '{sheet_name}' 数据为空或无法读取")

        except Exception as e:
            error_msg = f"❌ 加载字段失败: {e}"
            self.status_updated.emit(error_msg)
            self.logger.error(f"加载Sheet字段失败: {sheet_name} - {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")

    def _show_preview_data(self, sheet_name: str, data: List[Dict]):
        """在预览选项卡显示数据（支持格式化）"""
        try:
            self.logger.info(f"🔍 [修复] 开始显示预览数据: {sheet_name} - {len(data)} 行")
            
            # 🔧 [修复] 注册当前字段类型到格式化引擎
            from src.modules.data_import.formatting_engine import get_formatting_engine
            formatting_engine = get_formatting_engine()
            
            # 从映射配置中注册字段类型
            if hasattr(self.mapping_tab, 'get_current_mapping_config'):
                mapping_config = self.mapping_tab.get_current_mapping_config()
                for excel_field, config in mapping_config.items():
                    field_type = config.get('field_type')
                    if field_type:
                        formatting_engine.register_field_type(excel_field, {'type': field_type})
            
            # 🔧 [修复] 确保映射配置已保存
            if hasattr(self.mapping_tab, 'save_current_config'):
                self.mapping_tab.save_current_config()
            
            # 获取当前的字段映射配置
            field_mappings = {}
            if hasattr(self.mapping_tab, 'get_current_mapping_config'):
                mapping_config = self.mapping_tab.get_current_mapping_config()
                self.logger.info(f"🔍 [修复] 获取到字段映射配置: {len(mapping_config)} 个字段")
                
                # 转换为预览需要的格式
                for excel_field, config in mapping_config.items():
                    field_type = config.get('field_type')
                    field_mappings[excel_field] = {
                        'field_type': field_type,
                        'data_type': config.get('data_type'),
                        'is_required': config.get('is_required', False)
                    }
                    self.logger.debug(f"🔍 [修复] 字段 '{excel_field}' 映射到类型: {field_type}")
            
            # 切换到预览选项卡
            config_details = self.config_details_widget
            if hasattr(config_details, 'setCurrentIndex'):
                config_details.setCurrentIndex(2)  # 预览验证选项卡

            # 更新预览组件，传入字段映射配置
            self.preview_tab.show_preview_data(sheet_name, data, field_mappings)
            self.logger.info(f"🔍 [修复] 预览数据显示完成 - {len(field_mappings)} 个字段映射")

        except Exception as e:
            self.logger.error(f"🔍 [修复] 显示预览数据失败: {e}")
            import traceback
            self.logger.error(f"🔍 [修复] 详细错误: {traceback.format_exc()}")

    def _open_unified_advanced_settings(self):
        """打开统一高级配置对话框"""
        try:
            self.logger.info("开始打开高级配置对话框...")
            from src.gui.advanced_config_dialog import AdvancedConfigDialog

            self.logger.info("AdvancedConfigDialog 导入成功，开始初始化...")
            dialog = AdvancedConfigDialog(self)
            self.logger.info("AdvancedConfigDialog 初始化完成")

            if hasattr(dialog, 'config_changed'):
                dialog.config_changed.connect(self._on_advanced_config_changed)
                self.logger.info("config_changed 信号连接成功")

            self.logger.info("准备显示高级配置对话框...")
            result = dialog.exec_()
            self.logger.info(f"高级配置对话框关闭，返回值: {result}")

            if result == dialog.Accepted:
                self.status_updated.emit("高级配置已更新")
                self.logger.info("统一高级配置已更新")

        except Exception as e:
            import traceback
            error_msg = f"打开高级配置失败: {e}"
            detailed_error = f"详细错误信息: {traceback.format_exc()}"
            self.status_updated.emit(error_msg)
            self.logger.error(error_msg)
            self.logger.error(detailed_error)

    def _on_advanced_config_changed(self, config):
        """处理高级配置变化"""
        try:
            self.logger.info(f"接收到高级配置变化: {config}")

            # 文件导入配置 - 优先处理
            file_config = config.get('file_import', {})
            if file_config:
                self._apply_file_import_config(file_config)

            # 字段映射配置 - 其次处理
            mapping_config = config.get('field_mapping', {})
            if mapping_config:
                self._apply_field_mapping_config(mapping_config)

            # 智能推荐配置
            smart_config = config.get('smart_recommendations', {})
            if smart_config:
                self._apply_smart_recommendations_config(smart_config)

            # 数据处理配置
            data_config = config.get('data_processing', {})
            if data_config:
                self._apply_data_processing_config(data_config)

            # 界面个性化配置
            ui_config = config.get('ui_customization', {})
            if ui_config:
                self._apply_ui_customization_config(ui_config)

            # 性能优化配置
            perf_config = config.get('performance', {})
            if perf_config:
                self._apply_performance_config(perf_config)

            # 保存配置
            self.advanced_config = config
            self.status_updated.emit("✅ 高级配置已应用")
            self.logger.info(f"所有高级配置域已成功应用: {list(config.keys())}")

        except Exception as e:
            error_msg = f"处理高级配置变化失败: {e}"
            self.status_updated.emit(error_msg)
            self.logger.error(error_msg)

    def _apply_file_import_config(self, config: Dict[str, Any]):
        """应用文件导入配置"""
        try:
            # 设置默认文件路径
            default_path = config.get('default_import_path', '').strip()
            if default_path:
                import os
                # 标准化路径分隔符
                default_path = os.path.normpath(default_path)

                if os.path.isfile(default_path):
                    # 如果是具体文件，直接加载
                    self.logger.info(f"应用默认导入文件: {default_path}")
                    self.current_file_path = default_path

                    # 检查UI是否已初始化，避免在UI创建前访问组件
                    if hasattr(self, 'file_path_label') and self.file_path_label is not None:
                        # 获取文件名（兼容Windows和Unix路径）
                        file_name = os.path.basename(default_path)
                        self.file_path_label.setText(f"文件: {file_name}")

                        # 加载Excel文件的Sheet信息
                        self._load_excel_sheets()
                    else:
                        # UI未初始化时，仅保存路径，稍后在UI初始化完成后应用
                        self.logger.info(f"UI未初始化，保存默认路径供后续应用: {default_path}")
                        # 保存待应用的文件路径
                        self._pending_file_path = default_path

                elif os.path.isdir(default_path):
                    # 如果是目录，设置为默认目录
                    self.logger.info(f"设置默认导入目录: {default_path}")
                    # 这里可以存储默认目录偏好
                    pass
                else:
                    self.logger.warning(f"默认文件路径不存在: {default_path}")
                    self.status_updated.emit(f"⚠️ 默认文件不存在: {os.path.basename(default_path)}")

            # 设置支持的文件格式
            supported_formats = config.get('supported_formats', ['xlsx', 'xls', 'csv'])
            if hasattr(self, 'file_dialog_filters'):
                # 更新文件对话框过滤器
                pass

            # 设置文件大小限制
            max_file_size = config.get('max_file_size_mb', 100)
            if hasattr(self.import_manager, 'set_max_file_size'):
                self.import_manager.set_max_file_size(max_file_size)

            # 设置编码检测
            auto_detect_encoding = config.get('auto_detect_encoding', True)
            if hasattr(self.import_manager, 'enable_encoding_detection'):
                self.import_manager.enable_encoding_detection(auto_detect_encoding)

            # 设置Sheet自动选择策略
            sheet_selection_strategy = config.get('sheet_selection_strategy', 'all')
            if hasattr(self, 'sheet_management_widget'):
                if hasattr(self.sheet_management_widget, 'set_selection_strategy'):
                    self.sheet_management_widget.set_selection_strategy(sheet_selection_strategy)

            # 应用Excel表结构配置
            excel_structure = {
                'data_start_row': config.get('data_start_row', 2),
                'header_row': config.get('header_row', 1),
                'skip_rows': config.get('skip_rows', 0),
                'skip_footer_rows': config.get('skip_footer_rows', 0),
                'auto_detect_header': config.get('auto_detect_header', True),
                'ignore_empty_rows': config.get('ignore_empty_rows', True)
            }

            # 应用到导入管理器
            if hasattr(self.import_manager, 'excel_importer'):
                if hasattr(self.import_manager.excel_importer, 'set_structure_config'):
                    self.import_manager.excel_importer.set_structure_config(excel_structure)

            # 保存结构配置供后续使用
            self.excel_structure_config = excel_structure

            self.logger.info(f"文件导入配置已应用，包括Excel表结构设置: {excel_structure}")

        except Exception as e:
            self.logger.error(f"应用文件导入配置失败: {e}")

    def _apply_field_mapping_config(self, config: Dict[str, Any]):
        """应用字段映射配置"""
        try:
            # 设置映射算法类型
            mapping_algorithm = config.get('mapping_algorithm', 'fuzzy_match')
            if hasattr(self.import_manager, 'mapping_engine'):
                engine = self.import_manager.mapping_engine
                if hasattr(engine, 'set_algorithm'):
                    engine.set_algorithm(mapping_algorithm)

            # 设置映射相似度阈值
            similarity_threshold = config.get('similarity_threshold', 0.8)
            if hasattr(self.import_manager, 'mapping_engine'):
                engine = self.import_manager.mapping_engine
                if hasattr(engine, 'set_similarity_threshold'):
                    engine.set_similarity_threshold(similarity_threshold)

            # 设置自动映射开关
            auto_mapping_enabled = config.get('auto_mapping_enabled', True)
            if hasattr(self, 'mapping_tab'):
                if hasattr(self.mapping_tab, 'set_auto_mapping'):
                    self.mapping_tab.set_auto_mapping(auto_mapping_enabled)

            # 设置必填字段检查
            required_field_check = config.get('required_field_check', True)
            if hasattr(self, 'mapping_tab'):
                if hasattr(self.mapping_tab, 'enable_required_check'):
                    self.mapping_tab.enable_required_check(required_field_check)

            # 设置字段类型验证
            field_type_validation = config.get('field_type_validation', True)
            if hasattr(self.import_manager, 'data_validator'):
                validator = self.import_manager.data_validator
                if hasattr(validator, 'enable_type_validation'):
                    validator.enable_type_validation(field_type_validation)

            # 设置映射历史保存
            save_mapping_history = config.get('save_mapping_history', True)
            if hasattr(self, 'mapping_tab'):
                if hasattr(self.mapping_tab, 'enable_history_save'):
                    self.mapping_tab.enable_history_save(save_mapping_history)

            self.logger.info("字段映射配置已应用")

        except Exception as e:
            self.logger.error(f"应用字段映射配置失败: {e}")

    def _apply_smart_recommendations_config(self, config: Dict[str, Any]):
        """应用智能推荐配置"""
        try:
            # 配置智能映射引擎（如果存在）
            if hasattr(self.import_manager, 'mapping_engine'):
                engine = self.import_manager.mapping_engine

                # 设置置信度阈值
                threshold = config.get('confidence_threshold', 70) / 100.0
                if hasattr(engine, 'set_confidence_threshold'):
                    engine.set_confidence_threshold(threshold)

                # 启用/禁用历史学习
                if hasattr(engine, 'enable_history_learning'):
                    engine.enable_history_learning(config.get('enable_history_learning', True))

                # 启用/禁用语义分析
                if hasattr(engine, 'enable_semantic_analysis'):
                    engine.enable_semantic_analysis(config.get('enable_semantic_analysis', True))

            # 应用到映射配置组件
            if hasattr(self, 'mapping_tab'):
                # 设置自动应用高置信度推荐
                auto_apply = config.get('auto_apply_high_confidence', False)
                if hasattr(self.mapping_tab, 'set_auto_apply_high_confidence'):
                    self.mapping_tab.set_auto_apply_high_confidence(auto_apply)

                # 设置置信度指示器显示
                if hasattr(self.mapping_tab, 'toggle_confidence_indicators'):
                    show_confidence = config.get('show_confidence_indicators', True)
                    self.mapping_tab.toggle_confidence_indicators(show_confidence)

            self.logger.info("智能推荐配置已应用")

        except Exception as e:
            self.logger.error(f"应用智能推荐配置失败: {e}")

    def _apply_data_processing_config(self, config: Dict[str, Any]):
        """应用数据处理配置"""
        try:
            # 应用到导入管理器
            if hasattr(self.import_manager, 'data_validator'):
                validator = self.import_manager.data_validator

                # 设置严格验证模式
                if hasattr(validator, 'set_strict_mode'):
                    validator.set_strict_mode(config.get('strict_validation', False))

                # 设置空值处理策略
                null_strategy = config.get('null_value_strategy', 0)
                strategies = ['keep', 'default', 'skip', 'prompt']
                if hasattr(validator, 'set_null_strategy'):
                    validator.set_null_strategy(strategies[null_strategy])

                # 设置数据类型自动转换
                if hasattr(validator, 'enable_auto_conversion'):
                    validator.enable_auto_conversion(config.get('auto_type_conversion', True))

            # 应用到多表导入器
            if hasattr(self.import_manager, 'multi_sheet_importer'):
                importer = self.import_manager.multi_sheet_importer

                # 设置批量大小
                batch_size = config.get('batch_size', 1000)
                if hasattr(importer, 'set_batch_size'):
                    importer.set_batch_size(batch_size)

                # 设置错误容忍度
                tolerance = config.get('error_tolerance', 10) / 100.0
                if hasattr(importer, 'set_error_tolerance'):
                    importer.set_error_tolerance(tolerance)

            self.logger.info("数据处理配置已应用")

        except Exception as e:
            self.logger.error(f"应用数据处理配置失败: {e}")

    def _apply_ui_customization_config(self, config: Dict[str, Any]):
        """应用界面个性化配置"""
        try:
            # 设置表格显示行数限制
            row_limit = config.get('table_row_limit', 200)
            if hasattr(self, 'preview_tab'):
                if hasattr(self.preview_tab, 'set_row_limit'):
                    self.preview_tab.set_row_limit(row_limit)

            # 设置详细日志显示
            show_detailed = config.get('show_detailed_logs', False)
            if hasattr(self.logger, 'setLevel'):
                import logging
                self.logger.setLevel(logging.DEBUG if show_detailed else logging.INFO)

            # 设置置信度指示器显示
            show_confidence = config.get('show_confidence_indicators', True)
            if hasattr(self, 'mapping_tab'):
                if hasattr(self.mapping_tab, 'toggle_confidence_indicators'):
                    self.mapping_tab.toggle_confidence_indicators(show_confidence)

            # 设置自动保存间隔
            auto_save_interval = config.get('auto_save_interval', 5)
            if auto_save_interval > 0:
                # 启动自动保存计时器（如果需要的话）
                pass

            # 设置确认对话框显示
            self.show_confirmations = config.get('show_confirmation_dialogs', True)

            self.logger.info("界面个性化配置已应用")

        except Exception as e:
            self.logger.error(f"应用界面个性化配置失败: {e}")

    def _apply_performance_config(self, config: Dict[str, Any]):
        """应用性能优化配置"""
        try:
            # 设置最大内存使用限制
            max_memory = config.get('max_memory_usage', 2048)
            # 这里可以设置内存监控和限制机制

            # 启用/禁用缓存
            enable_cache = config.get('enable_caching', True)
            if hasattr(self.import_manager, 'enable_caching'):
                self.import_manager.enable_caching(enable_cache)

            # 设置预加载数据
            preload = config.get('preload_data', False)
            if hasattr(self.import_manager, 'set_preload_data'):
                self.import_manager.set_preload_data(preload)

            # 设置处理线程数
            thread_count = config.get('thread_count', 4)
            if hasattr(self.import_manager, 'set_thread_count'):
                self.import_manager.set_thread_count(thread_count)

            # 启用/禁用异步处理
            enable_async = config.get('enable_async_processing', True)
            if hasattr(self.import_manager, 'enable_async_processing'):
                self.import_manager.enable_async_processing(enable_async)

            # 设置进度更新频率
            update_freq = config.get('progress_update_frequency', 100)
            if hasattr(self, 'progress_bar'):
                # 可以设置进度条更新频率
                pass

            self.logger.info("应用性能优化配置完成")

        except Exception as e:
            self.logger.error(f"应用性能优化配置失败: {e}")

    def _on_current_sheet_changed(self, sheet_name: str, sheet_config):
        """
        🔧 [方案B实施] 当前Sheet变化处理 - 增强版本控制版本

        Args:
            sheet_name: Sheet名称
            sheet_config: Sheet配置对象
        """
        try:
            self.logger.info(f"🔧 [方案B] 当前Sheet变化: {sheet_name}")

            # 🔧 [方案B] 在切换Sheet前，确保所有字段配置已保存
            # 停止任何待执行的延迟保存定时器
            if hasattr(self.mapping_tab, 'save_timer') and self.mapping_tab.save_timer:
                self.mapping_tab.save_timer.stop()
                self.logger.info("🔧 [方案B] 停止延迟保存定时器")
            
            # 🔧 [方案B] 智能保存：只保存用户明确修改的字段配置
            save_success = False
            if hasattr(self.mapping_tab, '_smart_save_user_modified_configs'):
                save_success = self.mapping_tab._smart_save_user_modified_configs()
            else:
                self.logger.warning("🔧 [方案B] mapping_tab没有_smart_save_user_modified_configs方法")
            
            if save_success:
                self.logger.info("🔧 [方案B] 用户修改的字段配置已智能保存")
            else:
                self.logger.warning("🔧 [方案B] 智能保存失败，尝试强制保存")
                
                # 🔧 [方案B] 备用：强制保存当前表格中所有字段的最新状态
                if hasattr(self.mapping_tab, '_force_save_all_field_configs'):
                    save_success = self.mapping_tab._force_save_all_field_configs()
                    if save_success:
                        self.logger.info("🔧 [方案B] 已强制保存当前Sheet的所有字段配置")
                    else:
                        self.logger.warning("🔧 [方案B] 强制保存当前Sheet配置失败")
                
                # 🔧 [方案B] 备用保存机制：立即保存当前Sheet的字段映射配置
                if not save_success and hasattr(self.mapping_tab, '_save_mapping_config_immediately'):
                    save_success = self.mapping_tab._save_mapping_config_immediately()
                    if save_success:
                        self.logger.info("🔧 [方案B] 已保存当前Sheet的字段映射配置")
                    else:
                        self.logger.warning("🔧 [方案B] 保存当前Sheet的字段映射配置失败")
            
            # 🔧 [方案B] 如果保存失败，记录警告但不阻止切换
            if not save_success:
                self.logger.warning("⚠️ 🔧 [方案B] Sheet切换前配置保存失败，可能导致配置丢失")

            # 🔧 [方案B] 清理格式化引擎中的临时字段类型
            try:
                from src.modules.data_import.formatting_engine import get_formatting_engine
                formatting_engine = get_formatting_engine()
                
                if hasattr(formatting_engine, 'clear_temporary_field_types'):
                    formatting_engine.clear_temporary_field_types()
                    self.logger.info("🔧 [方案B] 已清理临时字段类型")
                else:
                    self.logger.debug("🔧 [方案B] 格式化引擎不支持清理临时字段类型")
            except Exception as e:
                self.logger.warning(f"🔧 [方案B] 清理临时字段类型失败: {e}")

            # 更新数据处理选项卡
            self.processing_tab.update_for_sheet(sheet_name, sheet_config)

            # 更新字段映射选项卡（调用mapping_tab的方法）
            if hasattr(self.mapping_tab, 'load_excel_headers_with_saved_config'):
                # 获取当前Sheet的字段信息
                current_file_path = self.current_file_path
                if current_file_path and hasattr(self, 'import_manager') and self.import_manager:
                    try:
                        # 从Excel文件获取字段头信息
                        df = self.import_manager.excel_importer.import_data(
                            current_file_path, sheet_name, max_rows=1
                        )
                        
                        if df is not None and not df.empty:
                            headers = list(df.columns)
                            self.logger.info(f"🔧 [修复] 为Sheet '{sheet_name}' 获取到 {len(headers)} 个字段")
                            
                            # 获取表类型
                            table_type = getattr(sheet_config, 'table_type', self.current_table_type)
                            if not table_type:
                                table_type = "💰 工资表"
                            
                            # 加载保存的配置
                            saved_configs = {}
                            if hasattr(self.mapping_tab, '_load_sheet_specific_config_early'):
                                saved_configs = self.mapping_tab._load_sheet_specific_config_early(sheet_name)
                            else:
                                self.logger.warning("🔧 [修复] mapping_tab没有_load_sheet_specific_config_early方法")
                            
                            # 调用mapping_tab的方法加载字段
                            self.mapping_tab.load_excel_headers_with_saved_config(headers, table_type, saved_configs)
                            
                            self.logger.info(f"🔧 [修复] Sheet '{sheet_name}' 字段映射已更新")
                        else:
                            self.logger.warning(f"🔧 [修复] Sheet '{sheet_name}' 数据为空，无法获取字段")
                    except Exception as e:
                        self.logger.error(f"🔧 [修复] 更新字段映射失败: {e}")
                else:
                    self.logger.warning("🔧 [修复] 无法获取文件路径或导入管理器")
            else:
                self.logger.warning("🔧 [修复] mapping_tab 不支持 load_excel_headers_with_saved_config 方法")

            # 更新预览验证选项卡（如果有相关方法）
            if hasattr(self.preview_tab, 'update_for_sheet'):
                self.preview_tab.update_for_sheet(sheet_name, sheet_config)

            # 更新状态
            self.status_updated.emit(f"已切换到Sheet: {sheet_name}")

        except Exception as e:
            self.logger.error(f"🔧 [方案B] 处理Sheet变化失败: {e}")

    def _on_processing_config_changed(self, config_dict: dict):
        """
        数据处理配置变化处理

        Args:
            config_dict: 配置字典
        """
        try:
            # 获取当前Sheet名称
            current_sheet = self.sheet_management_widget.sheet_config_manager.current_sheet
            if current_sheet:
                # 更新Sheet配置
                success = self.sheet_management_widget.update_sheet_config(current_sheet, **config_dict)
                if success:
                    self.logger.debug(f"Sheet '{current_sheet}' 配置已更新")
                else:
                    self.logger.warning(f"Sheet '{current_sheet}' 配置更新失败")

        except Exception as e:
            self.logger.error(f"处理数据处理配置变化失败: {e}")

    def _on_processing_preview_requested(self, sheet_name: str):
        """
        数据处理预览请求处理

        Args:
            sheet_name: Sheet名称
        """
        try:
            # 切换到预览验证选项卡
            self.config_tab_widget.setCurrentWidget(self.preview_tab)

            # 触发预览更新（如果预览组件支持）
            if hasattr(self.preview_tab, 'preview_sheet'):
                self.preview_tab.preview_sheet(sheet_name)

            self.status_updated.emit(f"正在预览Sheet: {sheet_name}")

        except Exception as e:
            self.logger.error(f"处理预览请求失败: {e}")

    def _on_field_type_changed(self, type_id: str):
        """🔧 [修复] 字段类型变更处理"""
        try:
            self.logger.info(f"🔧 [修复] 字段类型 {type_id} 已变更，刷新相关配置")

            # 🔧 [修复] 首先重新加载格式化引擎的自定义字段类型
            from src.modules.data_import.formatting_engine import get_formatting_engine
            formatting_engine = get_formatting_engine()
            formatting_engine.reload_custom_field_types()

            # 刷新映射配置中的字段类型选择
            if hasattr(self.mapping_tab, 'refresh_field_types'):
                self.mapping_tab.refresh_field_types()

            # 刷新数据处理配置中的验证规则
            if hasattr(self.processing_tab, 'update_validation_rules'):
                self.processing_tab.update_validation_rules()

            # 🔧 [修复] 刷新预览验证中的格式化效果，确保立即生效
            if hasattr(self.preview_tab, 'refresh_preview_with_formatting'):
                self.preview_tab.refresh_preview_with_formatting()
            elif hasattr(self.preview_tab, 'refresh_preview'):
                self.preview_tab.refresh_preview()

            self.status_updated.emit(f"🔧 [修复] 字段类型 {type_id} 配置已更新并应用")

        except Exception as e:
            self.logger.error(f"处理字段类型变更失败: {e}")

    def _on_field_type_deleted(self, type_id: str):
        """字段类型删除处理"""
        try:
            self.logger.info(f"字段类型 {type_id} 已删除，清理相关配置")

            # 清理映射配置中对该类型的引用
            if hasattr(self.mapping_tab, 'remove_field_type_references'):
                self.mapping_tab.remove_field_type_references(type_id)

            # 清理数据处理配置中的相关设置
            if hasattr(self.processing_tab, 'remove_field_type_references'):
                self.processing_tab.remove_field_type_references(type_id)

            # 刷新预览
            if hasattr(self.preview_tab, 'refresh_preview'):
                self.preview_tab.refresh_preview()

            self.status_updated.emit(f"字段类型 {type_id} 已删除，相关配置已清理")

        except Exception as e:
            self.logger.error(f"处理字段类型删除失败: {e}")




class UnifiedImportManager:
    """统一导入管理器 - 核心业务协调"""

    def __init__(self):
        """初始化导入管理器"""
        self.logger = setup_logger(__name__)

        # 初始化原有业务组件
        self.excel_importer = ExcelImporter()
        self.data_validator = DataValidator()
        self.table_manager = DynamicTableManager()
        self.multi_sheet_importer = MultiSheetImporter(self.table_manager)

        # 初始化新核心组件
        from src.gui.core import SmartMappingEngine, TemplateManager, ValidationEngine
        self.mapping_engine = SmartMappingEngine()
        self.template_manager = TemplateManager()
        self.validation_engine = ValidationEngine()

        # 初始化第二阶段功能组件
        from src.modules.data_import.sheet_config_manager import SheetConfigManager
        self.sheet_config_manager = SheetConfigManager()

        # 导入会话状态
        self.current_session = None

        self.logger.info("统一导入管理器初始化完成（包含第二阶段功能）")

    def analyze_excel_file(self, file_path: str) -> List[Dict]:
        """分析Excel文件并返回Sheet信息"""
        try:
            # 使用ExcelImporter获取Sheet信息
            sheet_names = self.excel_importer.get_sheet_names(file_path)

            sheet_info = []
            for sheet_name in sheet_names:
                # 获取每个Sheet的基本信息
                try:
                    data = self.excel_importer.import_data(file_path, sheet_name, max_rows=5)
                    row_count = len(data) if data else 0

                    sheet_info.append({
                        'name': sheet_name,
                        'row_count': row_count,
                        'enabled': True,
                        'status': 'ready'
                    })
                except Exception as e:
                    sheet_info.append({
                        'name': sheet_name,
                        'row_count': 0,
                        'enabled': False,
                        'status': f'error: {e}'
                    })

            return sheet_info

        except Exception as e:
            self.logger.error(f"分析Excel文件失败: {e}")
            raise

    def initialize_import_session(self, file_path: str, table_type: str):
        """初始化导入会话"""
        try:
            self.current_session = {
                'file_path': file_path,
                'table_type': table_type,
                'sheets': self.analyze_excel_file(file_path),
                'mapping_config': {},
                'processing_config': {},
                'created_time': self.logger.handlers[0].formatter.formatTime(
                    self.logger.makeRecord('', 0, '', 0, '', (), None)
                ) if self.logger.handlers else None
            }

            self.logger.info(f"导入会话已初始化: {table_type} - {file_path}")

        except Exception as e:
            self.logger.error(f"初始化导入会话失败: {e}")
            raise


class EnhancedSheetManagementWidget(QWidget):
    """增强的Sheet管理组件"""

    # 信号定义
    sheet_selection_changed = pyqtSignal(list)  # Sheet选择变化信号
    sheet_preview_requested = pyqtSignal(str)   # Sheet预览请求信号
    import_strategy_changed = pyqtSignal(str)   # 导入策略变化信号
    current_sheet_changed = pyqtSignal(str, object)  # 当前Sheet变化信号 (sheet_name, config)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)

        # 初始化数据
        self.sheet_info = []
        self.current_strategy = "merge_to_single_table"

        # 初始化Sheet配置管理器
        from src.modules.data_import.sheet_config_manager import SheetConfigManager
        self.sheet_config_manager = SheetConfigManager()

        self._init_ui()
        self._connect_signals()

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 工具栏
        toolbar = self._create_toolbar()
        layout.addWidget(toolbar)

        # Sheet列表
        self.sheet_tree = self._create_sheet_tree()
        layout.addWidget(self.sheet_tree, 1)

        # 导入策略
        strategy_group = self._create_strategy_group()
        layout.addWidget(strategy_group)

        # 状态信息
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #666; font-size: 12px;")
        layout.addWidget(self.status_label)

    def _create_toolbar(self) -> QWidget:
        """创建工具栏"""
        toolbar = QFrame()
        toolbar.setFrameStyle(QFrame.StyledPanel)
        toolbar.setMaximumHeight(45)  # 恢复单行高度

        layout = QHBoxLayout(toolbar)
        layout.setContentsMargins(10, 5, 10, 5)
        layout.setSpacing(8)

        # 所有按钮一行显示
        self.select_all_btn = QPushButton("全选")
        self.deselect_all_btn = QPushButton("全不选")
        self.refresh_btn = QPushButton("刷新")
        self.preview_btn = QPushButton("预览")
        self.analyze_btn = QPushButton("分析")

        # 设置按钮样式
        for btn in [self.select_all_btn, self.deselect_all_btn,
                   self.refresh_btn, self.preview_btn, self.analyze_btn]:
            btn.setMinimumHeight(32)
            btn.setMinimumWidth(70)

        # 设置按钮工具提示
        self.select_all_btn.setToolTip("选择所有工作表进行导入")
        self.deselect_all_btn.setToolTip("取消选择所有工作表")
        self.refresh_btn.setToolTip("重新扫描Excel文件中的工作表")
        self.preview_btn.setToolTip("预览选中工作表的数据内容")
        self.analyze_btn.setToolTip("分析工作表结构和数据质量")

        layout.addWidget(self.select_all_btn)
        layout.addWidget(self.deselect_all_btn)
        layout.addWidget(self.refresh_btn)
        layout.addWidget(self.preview_btn)
        layout.addWidget(self.analyze_btn)
        layout.addStretch()

        return toolbar

    def _create_sheet_tree(self) -> QTreeWidget:
        """创建Sheet列表树"""
        tree = QTreeWidget()
        tree.setHeaderLabels(["Sheet名称", "状态", "记录数", "操作"])

        # 设置响应式列宽
        self._setup_sheet_tree_responsive_columns(tree)

        # 设置属性
        tree.setAlternatingRowColors(True)
        tree.setRootIsDecorated(False)
        tree.setSelectionMode(QTreeWidget.ExtendedSelection)

        # 设置表头自适应策略
        header = tree.header()
        header.setStretchLastSection(False)  # 最后一列不自动拉伸
        header.setSectionResizeMode(header.Interactive)  # 允许用户调整列宽

        return tree

    def _setup_sheet_tree_responsive_columns(self, tree: QTreeWidget):
        """设置Sheet树响应式列宽"""
        try:
            # 获取左侧面板的宽度（Sheet管理面板）
            panel_width = 250  # 默认左侧面板宽度
            if hasattr(self, 'main_splitter') and self.main_splitter.sizes():
                panel_width = self.main_splitter.sizes()[0]

            # 预留边距和滚动条宽度
            available_width = max(panel_width - 30, 200)  # 最小200px

            # 定义各列的相对宽度比例和最小宽度
            column_configs = [
                {'ratio': 0.50, 'min_width': 80},   # Sheet名称 - 50%
                {'ratio': 0.20, 'min_width': 40},   # 状态 - 20%
                {'ratio': 0.20, 'min_width': 50},   # 记录数 - 20%
                {'ratio': 0.10, 'min_width': 30}    # 操作 - 10%
            ]

            # 计算并设置列宽
            for col, config in enumerate(column_configs):
                calculated_width = int(available_width * config['ratio'])
                final_width = max(calculated_width, config['min_width'])
                tree.setColumnWidth(col, final_width)

            self.logger.debug(f"Sheet树响应式列宽设置完成: 面板宽度={panel_width}px, 可用宽度={available_width}px")

        except Exception as e:
            # 如果响应式设置失败，使用默认固定宽度
            default_widths = [120, 60, 60, 60]
            for col, width in enumerate(default_widths):
                if col < tree.columnCount():
                    tree.setColumnWidth(col, width)
            self.logger.warning(f"Sheet树响应式列宽设置失败，使用默认宽度: {e}")

    def _create_strategy_group(self) -> QGroupBox:
        """创建导入策略组"""
        strategy_group = QGroupBox("导入策略")
        strategy_layout = QVBoxLayout(strategy_group)

        # 策略选择单选按钮
        from PyQt5.QtWidgets import QRadioButton, QButtonGroup

        self.strategy_group = QButtonGroup()

        self.merge_radio = QRadioButton("📝 合并到单表")
        self.merge_radio.setChecked(True)
        self.merge_radio.setToolTip("将所有选中的Sheet数据合并到一个表中")

        self.separate_radio = QRadioButton("📄 分别创建表")
        self.separate_radio.setToolTip("为每个Sheet创建独立的数据表")

        self.strategy_group.addButton(self.merge_radio, 0)
        self.strategy_group.addButton(self.separate_radio, 1)

        strategy_layout.addWidget(self.merge_radio)
        strategy_layout.addWidget(self.separate_radio)

        # 策略描述
        self.strategy_desc = QLabel("将多个Sheet的数据合并到一个统一的表中")
        self.strategy_desc.setStyleSheet("color: #666; font-size: 11px; padding: 5px;")
        self.strategy_desc.setWordWrap(True)
        strategy_layout.addWidget(self.strategy_desc)

        return strategy_group

    def _connect_signals(self):
        """连接信号"""
        # 工具栏按钮
        self.select_all_btn.clicked.connect(self._select_all_sheets)
        self.deselect_all_btn.clicked.connect(self._deselect_all_sheets)
        self.refresh_btn.clicked.connect(self._refresh_sheets)
        self.preview_btn.clicked.connect(self._preview_selected_sheet)
        self.analyze_btn.clicked.connect(self._analyze_sheets)

        # Sheet树选择变化
        self.sheet_tree.itemChanged.connect(self._on_sheet_selection_changed)
        self.sheet_tree.currentItemChanged.connect(self._on_current_sheet_changed)

        # 策略变化
        self.strategy_group.buttonClicked.connect(self._on_strategy_changed)

    def load_sheets(self, sheet_info: List[Dict]):
        """加载Sheet信息"""
        self.sheet_info = sheet_info
        self.sheet_tree.clear()

        from PyQt5.QtWidgets import QTreeWidgetItem

        for sheet in sheet_info:
            item = QTreeWidgetItem()
            item.setText(0, sheet['name'])

            # 状态显示
            status = sheet.get('status', 'ready')
            if status == 'ready':
                item.setText(1, "✅")
                item.setToolTip(1, "就绪")
            elif 'error' in status:
                item.setText(1, "❌")
                item.setToolTip(1, f"错误: {status}")
            else:
                item.setText(1, "⏳")
                item.setToolTip(1, "处理中")

            # 记录数
            row_count = sheet.get('row_count', 0)
            item.setText(2, str(row_count))

            # 操作按钮
            if row_count > 0:
                item.setText(3, "📊")
                item.setToolTip(3, "点击预览数据")
            else:
                item.setText(3, "❌")
                item.setToolTip(3, "无数据")

            # 设置复选框
            item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
            item.setCheckState(0, Qt.Checked if sheet.get('enabled', True) else Qt.Unchecked)

            # 存储原始数据
            item.setData(0, Qt.UserRole, sheet)

            self.sheet_tree.addTopLevelItem(item)

        self.status_label.setText(f"已加载 {len(sheet_info)} 个工作表")
        self.logger.info(f"已加载 {len(sheet_info)} 个Sheet")

        # 触发选择变化事件
        self._on_sheet_selection_changed()

    def _select_all_sheets(self):
        """全选Sheet"""
        count = 0
        for i in range(self.sheet_tree.topLevelItemCount()):
            item = self.sheet_tree.topLevelItem(i)
            item.setCheckState(0, Qt.Checked)
            count += 1

        if count > 0:
            self.status_label.setText(f"✅ 已全选 {count} 个工作表")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.status_label.setText("⚠️ 没有可选择的工作表，请先选择Excel文件")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")

    def _deselect_all_sheets(self):
        """取消全选"""
        count = 0
        for i in range(self.sheet_tree.topLevelItemCount()):
            item = self.sheet_tree.topLevelItem(i)
            item.setCheckState(0, Qt.Unchecked)
            count += 1

        if count > 0:
            self.status_label.setText(f"✅ 已取消选择 {count} 个工作表")
            self.status_label.setStyleSheet("color: blue; font-weight: bold;")
        else:
            self.status_label.setText("⚠️ 没有可取消的工作表")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")

    def _refresh_sheets(self):
        """刷新Sheet信息"""
        if self.sheet_info:
            self.load_sheets(self.sheet_info)
            self.status_label.setText(f"✅ 工作表信息已刷新 ({len(self.sheet_info)} 个工作表)")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.status_label.setText("⚠️ 没有可刷新的工作表信息，请先选择Excel文件")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")

    def _preview_selected_sheet(self):
        """预览选中的Sheet"""
        current_item = self.sheet_tree.currentItem()
        if current_item:
            sheet_data = current_item.data(0, Qt.UserRole)
            if sheet_data:
                self.sheet_preview_requested.emit(sheet_data['name'])
                self.status_label.setText(f"✅ 正在预览工作表: {sheet_data['name']}")
                self.status_label.setStyleSheet("color: blue; font-weight: bold;")
            else:
                self.status_label.setText("❌ 无法获取工作表数据")
                self.status_label.setStyleSheet("color: red; font-weight: bold;")
        else:
            self.status_label.setText("⚠️ 请先选择要预览的工作表")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")

    def _analyze_sheets(self):
        """分析Sheet结构"""
        selected_sheets = self.get_selected_sheets()
        if selected_sheets:
            # TODO: 实现Sheet结构分析
            self.status_label.setText(f"🔍 正在分析 {len(selected_sheets)} 个工作表的结构...")
            self.status_label.setStyleSheet("color: blue; font-weight: bold;")
            # 这里可以添加实际的分析逻辑
        else:
            self.status_label.setText("⚠️ 请先选择要分析的工作表")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")

    def _on_sheet_selection_changed(self):
        """Sheet选择变化处理"""
        selected_sheets = self.get_selected_sheets()
        self.sheet_selection_changed.emit(selected_sheets)

        # 更新状态
        if selected_sheets:
            self.status_label.setText(f"已选择 {len(selected_sheets)} 个工作表")
        else:
            self.status_label.setText("未选择工作表")

    def _on_current_sheet_changed(self, current, previous):
        """当前Sheet变化处理"""
        if current:
            sheet_data = current.data(0, Qt.UserRole)
            if sheet_data:
                sheet_name = sheet_data['name']
                self.logger.info(f"选中Sheet: {sheet_name}")

                # 切换到对应的Sheet配置
                sheet_config = self.sheet_config_manager.switch_sheet(sheet_name)

                # 发射当前Sheet变化信号，通知其他组件更新
                self.current_sheet_changed.emit(sheet_name, sheet_config)

                # 更新状态显示
                self.status_label.setText(f"当前Sheet: {sheet_name}")
                self.status_label.setStyleSheet("color: blue; font-weight: bold;")

    def _on_strategy_changed(self, button):
        """导入策略变化处理"""
        if button == self.merge_radio:
            self.current_strategy = "merge_to_single_table"
            self.strategy_desc.setText("将多个Sheet的数据合并到一个统一的表中，适用于结构相似的数据")
        else:
            self.current_strategy = "separate_tables"
            self.strategy_desc.setText("为每个Sheet创建独立的数据表，适用于结构不同的数据")

        self.import_strategy_changed.emit(self.current_strategy)
        self.logger.info(f"导入策略变更为: {self.current_strategy}")

    def get_selected_sheets(self) -> List[Dict]:
        """获取选中的Sheet列表"""
        selected = []
        for i in range(self.sheet_tree.topLevelItemCount()):
            item = self.sheet_tree.topLevelItem(i)
            if item.checkState(0) == Qt.Checked:
                sheet_data = item.data(0, Qt.UserRole)
                if sheet_data:
                    selected.append(sheet_data)
        return selected

    def get_import_strategy(self) -> str:
        """获取当前导入策略"""
        return self.current_strategy

    def set_excel_file(self, file_path: str) -> None:
        """
        设置Excel文件路径

        Args:
            file_path: Excel文件路径
        """
        self.sheet_config_manager.set_excel_file(file_path)
        self.logger.info(f"设置Excel文件路径: {file_path}")

    def get_sheet_config(self, sheet_name: str):
        """
        获取指定Sheet的配置

        Args:
            sheet_name: Sheet名称

        Returns:
            Sheet配置对象
        """
        return self.sheet_config_manager.get_or_create_config(sheet_name)

    def update_sheet_config(self, sheet_name: str, **kwargs) -> bool:
        """
        更新Sheet配置

        Args:
            sheet_name: Sheet名称
            **kwargs: 配置项

        Returns:
            是否更新成功
        """
        return self.sheet_config_manager.update_config(sheet_name, **kwargs)


class UnifiedMappingConfigWidget(QWidget):
    """统一映射配置组件"""

    # 信号定义
    mapping_changed = pyqtSignal()  # 映射配置变化信号
    validation_completed = pyqtSignal(bool)  # 验证完成信号

    def __init__(self, parent=None, db_manager=None, config_manager=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)

        # 保存依赖注入的管理器
        self.db_manager = db_manager
        self.config_manager = config_manager

        # 🔧 [P0-3修复] 防抖机制相关属性
        self._debounce_timers = {}  # 存储每个字段的防抖定时器
        self._debounce_delay = 500  # 防抖延迟时间（毫秒）

        # 初始化数据
        self.excel_headers = []
        self.current_table_type = ""
        self.mapping_config = {}
        self.validation_results = {}

        # 初始化核心组件
        from src.gui.core import SmartMappingEngine, TemplateManager, ValidationEngine
        from src.gui.performance_optimizer import get_performance_optimizer
        from src.modules.data_import.field_type_manager import FieldTypeManager

        self.mapping_engine = SmartMappingEngine()
        self.template_manager = TemplateManager()
        self.validation_engine = ValidationEngine()
        self.performance_optimizer = get_performance_optimizer()
        self.field_type_manager = FieldTypeManager()

        # 初始化配置同步管理器 - 第二步实施：实时保存机制
        self.config_sync_manager = None
        self._init_config_sync_manager()

        self._init_ui()
        self._connect_signals()

    def _init_config_sync_manager(self):
        """🔧 [P1-4修复] 初始化配置同步管理器 - 避免重复初始化"""
        self.logger.info("💾 [P1-4修复] 开始初始化ConfigSyncManager...")

        # 🔧 [P1-4修复] 检查是否已有全局实例
        if hasattr(self, 'config_sync_manager') and self.config_sync_manager is not None:
            self.logger.info("💾 [P1-4修复] ConfigSyncManager已存在，跳过重复初始化")
            return

        # 优先级1：尝试通过架构工厂获取ConfigSyncManager
        try:
            from src.core.architecture_factory import ArchitectureFactory
            if self.db_manager and self.config_manager:
                self.logger.debug("💾 [P1-1修复] 创建ArchitectureFactory实例")
                factory = ArchitectureFactory(self.db_manager, self.config_manager)

                # 🔧 [P1-1修复] 关键修复：调用初始化方法
                self.logger.debug("💾 [P1-1修复] 调用架构工厂初始化方法")
                init_success = factory.initialize_architecture()

                if init_success and factory.is_initialized():
                    self.logger.info("💾 [P1-1修复] ✅ 架构工厂初始化成功")
                    self.config_sync_manager = factory.get_config_sync_manager()
                    if self.config_sync_manager:
                        self.logger.info("💾 [P1-1修复] ✅ 通过架构工厂获取ConfigSyncManager成功")
                        return
                    else:
                        self.logger.warning("💾 [P1-1修复] 架构工厂返回的ConfigSyncManager为None")
                else:
                    self.logger.warning(f"💾 [P1-1修复] 架构工厂初始化失败，init_success={init_success}, is_initialized={factory.is_initialized()}")
            else:
                self.logger.info("💾 [P1修复] 缺少db_manager或config_manager，跳过架构工厂方案")
        except Exception as e:
            self.logger.warning(f"💾 [P1-1修复] 通过架构工厂获取ConfigSyncManager失败: {e}")
            import traceback
            self.logger.debug(f"💾 [P1-1修复] 详细错误信息: {traceback.format_exc()}")

        # 🔧 [P1-4修复] 优先级2：检查是否有全局单例实例
        try:
            from src.modules.data_import.config_sync_manager import get_global_config_sync_manager
            self.config_sync_manager = get_global_config_sync_manager()
            if self.config_sync_manager:
                self.logger.info("💾 [P1-4修复] ✅ 使用全局ConfigSyncManager实例")
                return
        except (ImportError, AttributeError):
            self.logger.debug("💾 [P1-4修复] 全局ConfigSyncManager不可用，使用备用方案")

        # 优先级3：创建独立实例（主要备用方案）
        try:
            from src.modules.data_import.config_sync_manager import ConfigSyncManager
            self.config_sync_manager = ConfigSyncManager()
            if self.config_sync_manager:
                self.logger.info("💾 [P1-4修复] ✅ 创建独立ConfigSyncManager实例成功")
                return
        except Exception as e:
            self.logger.warning(f"💾 [P1修复] 创建独立ConfigSyncManager实例失败: {e}")

        # 优先级4：最终备用实例（不同参数）
        try:
            from src.modules.data_import.config_sync_manager import ConfigSyncManager
            # 使用显式路径参数
            config_path = "state/data/field_mappings.json"
            self.config_sync_manager = ConfigSyncManager(config_path=config_path)
            if self.config_sync_manager:
                self.logger.info("💾 [P1修复] ✅ 创建带路径的ConfigSyncManager实例成功")
                return
        except Exception as e:
            self.logger.error(f"💾 [P1修复] 创建带路径的ConfigSyncManager实例失败: {e}")

        # 优先级4：创建最简化的配置管理器（最后备用）
        try:
            # 创建最简化的配置管理器类
            class MinimalConfigSyncManager:
                def __init__(self):
                    self.logger = setup_logger(f"{__name__}.MinimalConfigSyncManager")
                    self.logger.info("💾 [P1修复] 最简化ConfigSyncManager已初始化")
                
                def save_field_mapping(self, table_name, excel_field, field_config):
                    self.logger.info(f"💾 [简化] 模拟保存字段映射: {table_name}.{excel_field} = {field_config}")
                    return True
                
                def save_mapping(self, table_name, mapping, metadata=None):
                    self.logger.info(f"💾 [简化] 模拟保存映射: {table_name}, {len(mapping) if mapping else 0} 个字段")
                    return True
                    
            self.config_sync_manager = MinimalConfigSyncManager()
            self.logger.warning("💾 [P1修复] ⚠️ 使用最简化ConfigSyncManager（功能受限）")
            return
            
        except Exception as final_e:
            self.logger.error(f"💾 [P1修复] ❌ 所有ConfigSyncManager初始化方案均失败: {final_e}")
            self.config_sync_manager = None

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 映射工具栏
        toolbar = self._create_mapping_toolbar()
        layout.addWidget(toolbar)

        # 映射配置表格
        self.mapping_table = self._create_mapping_table()
        layout.addWidget(self.mapping_table, 1)

        # 状态栏
        status_layout = QHBoxLayout()
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("color: #666; font-size: 12px;")
        self.validation_label = QLabel("")
        self.validation_label.setStyleSheet("font-weight: bold;")

        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.validation_label)

        layout.addLayout(status_layout)

    def _create_mapping_toolbar(self) -> QWidget:
        """创建映射工具栏"""
        toolbar = QFrame()
        toolbar.setFrameStyle(QFrame.StyledPanel)
        toolbar.setMaximumHeight(45)  # 恢复单行高度

        layout = QHBoxLayout(toolbar)
        layout.setContentsMargins(10, 5, 10, 5)
        layout.setSpacing(8)

        # 所有按钮一行显示
        self.smart_mapping_btn = QPushButton("智能映射")
        self.save_template_btn = QPushButton("保存模板")
        self.load_template_btn = QPushButton("加载模板")
        self.reset_mapping_btn = QPushButton("重置映射")
        self.validate_btn = QPushButton("验证配置")

        # 设置按钮样式
        for btn in [self.smart_mapping_btn, self.save_template_btn, self.load_template_btn,
                   self.reset_mapping_btn, self.validate_btn]:
            btn.setMinimumHeight(32)
            btn.setMaximumWidth(90)

        # 设置按钮工具提示
        self.smart_mapping_btn.setToolTip("基于字段名称智能推荐映射关系")
        self.save_template_btn.setToolTip("将当前映射配置保存为模板")
        self.load_template_btn.setToolTip("从已保存的模板加载映射配置")
        self.reset_mapping_btn.setToolTip("重置所有映射配置到初始状态")
        self.validate_btn.setToolTip("验证当前映射配置的正确性")

        # 重要按钮高亮
        self.smart_mapping_btn.setStyleSheet(
            "QPushButton { background-color: #2196F3; color: white; font-weight: bold; }"
            "QPushButton:hover { background-color: #1976D2; }"
        )

        layout.addWidget(self.smart_mapping_btn)
        layout.addWidget(self.save_template_btn)
        layout.addWidget(self.load_template_btn)
        layout.addWidget(self.reset_mapping_btn)
        layout.addWidget(self.validate_btn)
        layout.addStretch()

        return toolbar

    def _create_mapping_table(self) -> QTableWidget:
        """创建映射配置表格"""
        table = QTableWidget()
        table.setColumnCount(7)
        table.setHorizontalHeaderLabels([
            "Excel列名", "数据库字段", "显示名称", "字段类型", "数据类型", "是否必需", "验证状态"
        ])

        # 设置响应式列宽（初始设置，会在窗口大小变化时动态调整）
        self._setup_table_responsive_columns(table)

        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setSelectionMode(QTableWidget.SingleSelection)
        table.verticalHeader().setVisible(False)

        # 设置编辑触发器 - 允许双击和选中后按键编辑
        table.setEditTriggers(QTableWidget.DoubleClicked | QTableWidget.SelectedClicked | QTableWidget.EditKeyPressed)

        # 设置行高以确保编辑器有足够空间
        table.verticalHeader().setDefaultSectionSize(35)

        # 设置简洁美观的表格样式，只在编辑时显示特殊效果
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e9ecef;
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: #cfe2ff;
                selection-color: #0d6efd;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                font-size: 12px;
            }

            QTableWidget::item {
                padding: 1px;
                border: none;
                border-bottom: 1px solid #f1f3f4;
            }

            QTableWidget::item:selected {
                background-color: #cfe2ff;
                color: #0d6efd;
            }

            QTableWidget::item:hover {
                background-color: #e7f1ff;
            }

            /* 表头样式 */
            QHeaderView::section {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                padding: 10px 8px;
                border: 1px solid #dee2e6;
                border-radius: 0px;
                font-weight: 600;
                font-size: 12px;
                color: #495057;
            }

            QHeaderView::section:first {
                border-top-left-radius: 6px;
            }

            QHeaderView::section:last {
                border-top-right-radius: 6px;
            }

            /* 编辑时的低调提示 - 只改变背景色，不加边框 */
            QLineEdit {
                background-color: #fff9c4;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 6px 8px;
                font-size: 12px;
                color: #495057;
                min-height: 18px;
            }

            QLineEdit:focus {
                background-color: #fff9c4;
                border-color: #ffc107;
                outline: none;
            }
        """)

        # 设置表格自适应策略
        header = table.horizontalHeader()
        header.setStretchLastSection(False)  # 最后一列不自动拉伸
        header.setSectionResizeMode(header.Interactive)  # 允许用户调整列宽

        return table

    def _setup_table_responsive_columns(self, table: QTableWidget):
        """设置表格响应式列宽"""
        try:
            # 获取表格父容器宽度，如果未初始化则使用默认值
            parent_width = table.parent().width() if table.parent() else 800
            if parent_width < 400:
                parent_width = 800  # 默认宽度

            # 预留滚动条和边距的宽度
            available_width = parent_width - 40

            # 定义各列的相对宽度比例 (7列: Excel列名, 数据库字段, 显示名称, 字段类型, 数据类型, 是否必需, 验证状态)
            column_ratios = [0.20, 0.20, 0.18, 0.18, 0.12, 0.08, 0.04]

            # 定义各列的最小宽度
            min_widths = [120, 120, 100, 100, 80, 70, 50]

            # 计算并设置列宽
            for col, (ratio, min_width) in enumerate(zip(column_ratios, min_widths)):
                calculated_width = int(available_width * ratio)
                final_width = max(calculated_width, min_width)
                table.setColumnWidth(col, final_width)

            self.logger.debug(f"表格响应式列宽设置完成: 可用宽度={available_width}px")

        except Exception as e:
            # 如果响应式设置失败，使用默认固定宽度
            default_widths = [150, 150, 120, 100, 80, 80]
            for col, width in enumerate(default_widths):
                if col < table.columnCount():
                    table.setColumnWidth(col, width)
            self.logger.warning(f"响应式列宽设置失败，使用默认宽度: {e}")

    def _connect_signals(self):
        """连接信号"""
        # 工具栏按钮
        self.smart_mapping_btn.clicked.connect(self._generate_smart_mapping)
        self.save_template_btn.clicked.connect(self._save_as_template)
        self.load_template_btn.clicked.connect(self._load_from_template)
        self.reset_mapping_btn.clicked.connect(self._reset_mapping)
        self.validate_btn.clicked.connect(self._validate_mapping)

        # 表格变化
        self.mapping_table.cellChanged.connect(self._on_mapping_changed)

    def _clean_field_name_for_display(self, field_name: str) -> str:
        """🔧 [P0-2修复] 清理字段名用于显示，移除换行符但保留可读性"""
        import re

        if not field_name:
            return "未命名字段"

        # 移除换行符、制表符，但保留空格
        cleaned = re.sub(r'[\r\n\t]', '', field_name.strip())

        # 将多个连续空格替换为单个空格
        cleaned = re.sub(r'\s+', ' ', cleaned)

        # 移除首尾空格
        cleaned = cleaned.strip()

        # 如果清理后为空，使用默认名称
        if not cleaned:
            cleaned = "未命名字段"

        return cleaned

    def _clean_field_name(self, field_name: str) -> str:
        """清理字段名，移除特殊字符（用于数据库字段名）"""
        import re

        if not field_name:
            return "field_name"

        # 移除所有空白字符（换行符、制表符、空格等）
        cleaned = re.sub(r'\s+', '', field_name.strip())

        # 移除特殊字符，只保留字母、数字、下划线和中文
        cleaned = re.sub(r'[^\w\u4e00-\u9fff]', '', cleaned)

        # 如果清理后为空，使用默认名称
        if not cleaned:
            cleaned = "field_name"

        # 确保不以数字开头（数据库字段名规范）
        if cleaned and cleaned[0].isdigit():
            cleaned = f"field_{cleaned}"

        return cleaned

    def load_excel_headers_with_saved_config(self, headers: List[str], table_type: str, saved_configs: dict):
        """🔧 [方案A实施] 加载Excel字段头 - 优先使用保存的配置，智能推断兜底"""
        self.excel_headers = headers
        self.current_table_type = table_type
        self.mapping_config = {}

        self.logger.info(f"🔧 [方案A实施] 加载Excel字段: {len(headers)} 个字段, 表类型: {table_type}")
        if saved_configs:
            self.logger.info(f"🔧 [方案A实施] 将优先使用 {len(saved_configs)} 个保存的配置")

        # 临时断开信号连接，避免在创建表格时触发变化事件
        self.mapping_table.cellChanged.disconnect()

        # 创建表格行
        self.mapping_table.setRowCount(len(headers))

        for row, header in enumerate(headers):
            # 🔧 [P0-2修复] 清理字段名，移除换行符等特殊字符
            original_header = header
            cleaned_header = self._clean_field_name_for_display(header)

            # Excel列名（只读）- 显示清理后的字段名
            excel_item = QTableWidgetItem(cleaned_header)
            excel_item.setFlags(excel_item.flags() & ~Qt.ItemIsEditable)
            excel_item.setToolTip(f"原字段名: {original_header}\n显示名: {cleaned_header}")
            self.mapping_table.setItem(row, 0, excel_item)

            # 数据库字段（输入框）- 优先使用保存的配置
            cleaned_field_name = self._clean_field_name(header)
            if header in saved_configs and saved_configs[header].get('target_field'):
                db_field_value = saved_configs[header]['target_field']
                self.logger.debug(f"🔧 [方案A实施] 字段 '{header}' 使用保存的数据库字段: {db_field_value}")
            else:
                db_field_value = cleaned_field_name
                self.logger.debug(f"🔧 [方案A实施] 字段 '{header}' 使用默认数据库字段: {db_field_value}")
            
            db_field_item = QTableWidgetItem(db_field_value)
            db_field_item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsEditable | Qt.ItemIsSelectable)
            db_field_item.setToolTip(f"原字段名: {original_header}\n清理后: {cleaned_field_name}")
            self.mapping_table.setItem(row, 1, db_field_item)

            # 显示名称（可编辑）- 优先使用保存的配置
            if header in saved_configs and saved_configs[header].get('display_name'):
                display_value = saved_configs[header]['display_name']
                self.logger.debug(f"🔧 [方案A实施] 字段 '{header}' 使用保存的显示名称: {display_value}")
            else:
                display_value = cleaned_header
                self.logger.debug(f"🔧 [方案A实施] 字段 '{header}' 使用默认显示名称: {display_value}")
            
            display_item = QTableWidgetItem(display_value)
            display_item.setToolTip(f"原字段名: {original_header}")
            self.mapping_table.setItem(row, 2, display_item)

            # 字段类型（下拉框）- 🔧 [方案A实施] 优先使用保存的配置，智能推断兜底
            if header in saved_configs and saved_configs[header].get('field_type'):
                saved_field_type = saved_configs[header]['field_type']
                self.logger.info(f"🔧 [方案A实施] 字段 '{header}' 使用保存的类型: {saved_field_type}")
                field_type_combo = self._create_field_type_combo(saved_field_type)
            else:
                recommended_type = self._get_recommended_field_type(header)
                self.logger.info(f"🔧 [方案A实施] 字段 '{header}' 使用智能推断类型: {recommended_type}")
                field_type_combo = self._create_field_type_combo(recommended_type)
            
            self.mapping_table.setCellWidget(row, 3, field_type_combo)
            
            # 💾 [方案一实施] 为字段类型ComboBox设置即时保存机制
            self._setup_field_type_combo_immediate_save(field_type_combo, row)

            # 数据类型（下拉框）- 🔧 [方案A实施] 优先使用保存的配置，智能推断兜底
            data_type_combo = QComboBox()
            data_type_combo.addItems(["VARCHAR(100)", "VARCHAR(255)", "INT", "DECIMAL(10,2)", "DATE", "TEXT"])
            
            if header in saved_configs and saved_configs[header].get('data_type'):
                saved_data_type = saved_configs[header]['data_type']
                self.logger.debug(f"🔧 [方案A实施] 字段 '{header}' 使用保存的数据类型: {saved_data_type}")
                data_type_combo.setCurrentText(saved_data_type)
            else:
                # 根据字段类型智能选择数据类型
                current_field_type = field_type_combo.currentData() or field_type_combo.currentText()
                recommended_data_type = self._get_recommended_data_type(current_field_type)
                self.logger.debug(f"🔧 [方案A实施] 字段 '{header}' 使用智能推断数据类型: {recommended_data_type}")
                data_type_combo.setCurrentText(recommended_data_type)

            # 设置统一的表格下拉框样式
            self._setup_table_combo_style(data_type_combo)
            self.mapping_table.setCellWidget(row, 4, data_type_combo)

            # 是否必需（复选框）- 🔧 [方案A实施] 优先使用保存的配置
            required_item = QTableWidgetItem()
            if header in saved_configs and 'is_required' in saved_configs[header]:
                required_state = Qt.Checked if saved_configs[header]['is_required'] else Qt.Unchecked
                self.logger.debug(f"🔧 [方案A实施] 字段 '{header}' 使用保存的必需状态: {saved_configs[header]['is_required']}")
            else:
                required_state = Qt.Unchecked
                self.logger.debug(f"🔧 [方案A实施] 字段 '{header}' 使用默认必需状态: False")
            
            required_item.setCheckState(required_state)
            required_item.setFlags(required_item.flags() & ~Qt.ItemIsEditable)
            self.mapping_table.setItem(row, 5, required_item)

            # 验证状态（只读图标）- 列索引从5改为6
            status_item = QTableWidgetItem("⏳")
            status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
            self.mapping_table.setItem(row, 6, status_item)

        self.status_label.setText(f"🔧 [方案A实施] 已加载 {len(headers)} 个字段（优先使用保存的配置）")

        # 重新连接信号（在表格完全创建后）
        self.mapping_table.cellChanged.connect(self._on_mapping_changed)

        # 连接下拉框信号
        for row in range(self.mapping_table.rowCount()):
            # 连接字段类型下拉框信号
            field_type_combo = self.mapping_table.cellWidget(row, 3)
            if field_type_combo:
                field_type_combo.currentTextChanged.connect(self._on_field_type_changed)

            # 连接数据类型下拉框信号
            data_type_combo = self.mapping_table.cellWidget(row, 4)
            if data_type_combo:
                data_type_combo.currentTextChanged.connect(self._on_mapping_changed)

        # 重新调整列宽以确保编辑体验
        self._setup_table_responsive_columns(self.mapping_table)

        self.logger.info(f"🔧 [方案A实施] 字段映射表格创建完成: {len(headers)} 行")

        # 自动生成智能映射
        if headers:
            self._generate_smart_mapping()

    def load_excel_headers(self, headers: List[str], table_type: str):
        """加载Excel字段头"""
        self.excel_headers = headers
        self.current_table_type = table_type
        self.mapping_config = {}

        self.logger.info(f"加载Excel字段: {len(headers)} 个字段, 表类型: {table_type}")

        # 临时断开信号连接，避免在创建表格时触发变化事件
        self.mapping_table.cellChanged.disconnect()

        # 创建表格行
        self.mapping_table.setRowCount(len(headers))

        for row, header in enumerate(headers):
            # 🔧 [P0-2修复] 清理字段名，移除换行符等特殊字符
            original_header = header
            cleaned_header = self._clean_field_name_for_display(header)

            # Excel列名（只读）- 显示清理后的字段名
            excel_item = QTableWidgetItem(cleaned_header)
            excel_item.setFlags(excel_item.flags() & ~Qt.ItemIsEditable)
            excel_item.setToolTip(f"原字段名: {original_header}\n显示名: {cleaned_header}")
            self.mapping_table.setItem(row, 0, excel_item)

            # 数据库字段（输入框）- 默认值为清理后的Excel列名
            cleaned_field_name = self._clean_field_name(header)
            db_field_item = QTableWidgetItem(cleaned_field_name)
            db_field_item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsEditable | Qt.ItemIsSelectable)
            db_field_item.setToolTip(f"原字段名: {original_header}\n清理后: {cleaned_field_name}")
            self.mapping_table.setItem(row, 1, db_field_item)

            # 显示名称（可编辑）- 使用清理后的字段名
            display_item = QTableWidgetItem(cleaned_header)
            display_item.setToolTip(f"原字段名: {original_header}")
            self.mapping_table.setItem(row, 2, display_item)

            # 字段类型（下拉框）- 新增列，使用智能推断
            recommended_type = self._get_recommended_field_type(header)
            field_type_combo = self._create_field_type_combo(recommended_type)
            self.mapping_table.setCellWidget(row, 3, field_type_combo)
            
            # 💾 [方案一实施] 为字段类型ComboBox设置即时保存机制
            self._setup_field_type_combo_immediate_save(field_type_combo, row)
            
            self.logger.info(f"🧠 [智能推断] 字段 '{header}' 推荐类型: {recommended_type}")

            # 数据类型（下拉框）- 列索引从3改为4，根据推荐字段类型智能选择
            data_type_combo = QComboBox()
            data_type_combo.addItems(["VARCHAR(100)", "VARCHAR(255)", "INT", "DECIMAL(10,2)", "DATE", "TEXT"])
            recommended_data_type = self._get_recommended_data_type(recommended_type)
            data_type_combo.setCurrentText(recommended_data_type)
            self.logger.info(f"🧠 [智能推断] 字段 '{header}' 推荐数据类型: {recommended_data_type}")

            # 设置统一的表格下拉框样式
            self._setup_table_combo_style(data_type_combo)

            self.mapping_table.setCellWidget(row, 4, data_type_combo)

            # 是否必需（复选框）- 列索引从4改为5
            required_item = QTableWidgetItem()
            required_item.setCheckState(Qt.Unchecked)
            required_item.setFlags(required_item.flags() & ~Qt.ItemIsEditable)
            self.mapping_table.setItem(row, 5, required_item)

            # 验证状态（只读图标）- 列索引从5改为6
            status_item = QTableWidgetItem("⏳")
            status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
            self.mapping_table.setItem(row, 6, status_item)

        self.status_label.setText(f"已加载 {len(headers)} 个字段")

        # 重新连接信号（在表格完全创建后）
        self.mapping_table.cellChanged.connect(self._on_mapping_changed)

        # 连接下拉框信号
        for row in range(self.mapping_table.rowCount()):
            # 连接字段类型下拉框信号
            field_type_combo = self.mapping_table.cellWidget(row, 3)
            if field_type_combo:
                field_type_combo.currentTextChanged.connect(self._on_field_type_changed)

            # 连接数据类型下拉框信号
            data_type_combo = self.mapping_table.cellWidget(row, 4)
            if data_type_combo:
                data_type_combo.currentTextChanged.connect(self._on_mapping_changed)

        # 重新调整列宽以确保编辑体验
        self._setup_table_responsive_columns(self.mapping_table)

        self.logger.info(f"字段映射表格创建完成: {len(headers)} 行")

        # 自动生成智能映射
        if headers:
            self._generate_smart_mapping()

    def get_current_mapping_config(self):
        """
        获取当前的字段映射配置
        
        Returns:
            Dict: 字段映射配置 {excel_field: {'field_type': xxx, 'data_type': xxx, ...}}
        """
        try:
            current_config = {}
            self.logger.info(f"🔧 [调试] 开始获取字段映射配置，表格行数: {self.mapping_table.rowCount()}")
            
            for row in range(self.mapping_table.rowCount()):
                # 获取Excel字段名
                excel_field_item = self.mapping_table.item(row, 0)
                if not excel_field_item:
                    self.logger.warning(f"🔧 [调试] 第{row}行Excel字段项为空，跳过")
                    continue
                
                excel_field = excel_field_item.text()
                self.logger.info(f"🔧 [调试] 处理第{row}行字段: '{excel_field}'")
                
                # 获取字段类型（第3列）
                field_type_combo = self.mapping_table.cellWidget(row, 3)
                if field_type_combo:
                    field_type_text = field_type_combo.currentText()
                    field_type_data = field_type_combo.currentData()
                    field_type = field_type_data
                    self.logger.info(f"🔧 [调试] 字段类型下拉框 - 显示: '{field_type_text}', 数据: '{field_type_data}'")
                else:
                    field_type = None
                    self.logger.warning(f"🔧 [调试] 第{row}行字段类型下拉框为空")
                
                # 获取数据类型（第4列）
                data_type_combo = self.mapping_table.cellWidget(row, 4)
                if data_type_combo:
                    data_type = data_type_combo.currentText()
                    self.logger.debug(f"🔧 [调试] 数据类型: '{data_type}'")
                else:
                    data_type = None
                    self.logger.warning(f"🔧 [调试] 第{row}行数据类型下拉框为空")
                
                # 获取是否必需（第5列）- 修复：这是QTableWidgetItem，不是复选框
                is_required_item = self.mapping_table.item(row, 5)
                if is_required_item:
                    is_required = is_required_item.checkState() == Qt.Checked
                    self.logger.debug(f"🔧 [调试] 是否必需: {is_required}")
                else:
                    is_required = False
                    self.logger.warning(f"🔧 [调试] 第{row}行必需项为空")
                
                # 获取显示名称（第2列）- 修复：这是QTableWidgetItem，不是下拉框
                display_name_item = self.mapping_table.item(row, 2)
                if display_name_item:
                    display_name = display_name_item.text()
                    self.logger.debug(f"🔧 [调试] 显示名称: '{display_name}'")
                else:
                    display_name = None
                    self.logger.warning(f"🔧 [调试] 第{row}行显示名称项为空")
                
                # 获取数据库字段（第1列）
                db_field_item = self.mapping_table.item(row, 1)
                if db_field_item:
                    db_field = db_field_item.text()
                    self.logger.debug(f"🔧 [调试] 数据库字段: '{db_field}'")
                else:
                    db_field = None
                    self.logger.warning(f"🔧 [调试] 第{row}行数据库字段项为空")
                
                current_config[excel_field] = {
                    'field_type': field_type,
                    'data_type': data_type,
                    'is_required': is_required,
                    'display_name': display_name,
                    'db_field': db_field
                }
                
                # 🔧 [调试] 记录最终配置
                self.logger.info(f"🔧 [调试] 字段 '{excel_field}' 最终配置: {current_config[excel_field]}")
            
            self.logger.info(f"🔧 [调试] 获取字段映射配置完成，共 {len(current_config)} 个字段")
            return current_config
            
        except Exception as e:
            self.logger.error(f"🔧 [调试] 获取当前映射配置失败: {e}")
            import traceback
            self.logger.error(f"🔧 [调试] 详细错误: {traceback.format_exc()}")
            return {}

    def _generate_smart_mapping(self):
        """生成智能映射"""
        if not self.excel_headers or not self.current_table_type:
            self.status_label.setText("⚠️ 请先选择Excel文件和表类型")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")
            return

        try:
            self.status_label.setText("🤖 正在分析字段并生成智能映射...")
            self.status_label.setStyleSheet("color: blue; font-weight: bold;")

            # 使用智能映射引擎生成建议
            mapping_results = self.mapping_engine.generate_smart_mapping(
                self.excel_headers, self.current_table_type
            )

            applied_count = 0
            high_confidence_count = 0

            # 应用映射结果到表格
            for result in mapping_results:
                row = self.excel_headers.index(result.source_field)

                # 更新数据库字段
                db_field_item = self.mapping_table.item(row, 1)
                if db_field_item:
                    db_field_item.setText(result.target_field)

                # 更新显示名称
                display_item = self.mapping_table.item(row, 2)
                if display_item:
                    display_item.setText(result.display_name)

                # 更新字段类型（第3列）- 根据字段名推荐字段类型
                field_type_combo = self.mapping_table.cellWidget(row, 3)
                if field_type_combo:
                    recommended_field_type = self._get_recommended_field_type(result.source_field)
                    index = field_type_combo.findData(recommended_field_type)
                    if index >= 0:
                        field_type_combo.setCurrentIndex(index)

                # 更新数据类型（第4列）
                data_type_combo = self.mapping_table.cellWidget(row, 4)
                if data_type_combo:
                    data_type_combo.setCurrentText(result.data_type)

                # 更新是否必需（第5列）
                required_item = self.mapping_table.item(row, 5)
                if required_item:
                    required_item.setCheckState(Qt.Checked if result.is_required else Qt.Unchecked)

                # 更新验证状态（第6列）
                status_item = self.mapping_table.item(row, 6)
                if status_item:
                    if result.confidence >= 0.8:
                        status_item.setText("✅")
                        status_item.setToolTip(f"高置信度 ({result.confidence:.1%}): {result.reasoning}")
                        high_confidence_count += 1
                    elif result.confidence >= 0.5:
                        status_item.setText("⚠️")
                        status_item.setToolTip(f"中等置信度 ({result.confidence:.1%}): {result.reasoning}")
                    else:
                        status_item.setText("❓")
                        status_item.setToolTip(f"低置信度 ({result.confidence:.1%}): {result.reasoning}")

                applied_count += 1

            self._update_mapping_config()

            # 显示详细结果
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(
                self, "智能映射完成",
                f"智能映射已完成！\n\n"
                f"• 总共处理字段: {len(self.excel_headers)} 个\n"
                f"• 成功映射字段: {applied_count} 个\n"
                f"• 高置信度映射: {high_confidence_count} 个\n"
                f"• 建议人工检查: {applied_count - high_confidence_count} 个"
            )

            self.status_label.setText(f"✅ 智能映射完成: {applied_count}/{len(self.excel_headers)} 个字段")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")
            self.logger.info("智能映射生成成功")

        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"智能映射失败: {str(e)}")
            self.status_label.setText("❌ 智能映射失败")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")
            self.logger.error(f"智能映射生成失败: {e}")

    def _save_as_template(self):
        """增强模板保存功能"""
        from PyQt5.QtWidgets import QInputDialog, QDialog, QVBoxLayout, QLineEdit, QTextEdit, QPushButton, QHBoxLayout, QLabel, QComboBox

        if not self.mapping_config:
            self.status_label.setText("请先配置字段映射")
            return

        # 创建增强的模板保存对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("保存映射模板")
        dialog.setFixedSize(450, 320)

        layout = QVBoxLayout(dialog)

        # 模板名称
        name_layout = QHBoxLayout()
        name_layout.addWidget(QLabel("模板名称:"))
        name_edit = QLineEdit()
        name_edit.setPlaceholderText("请输入模板名称")
        name_layout.addWidget(name_edit)
        layout.addLayout(name_layout)

        # 模板描述
        desc_layout = QVBoxLayout()
        desc_layout.addWidget(QLabel("模板描述:"))
        desc_edit = QTextEdit()
        desc_edit.setPlaceholderText("请输入模板描述（可选）")
        desc_edit.setMaximumHeight(60)
        desc_layout.addWidget(desc_edit)
        layout.addLayout(desc_layout)

        # 适用表类型
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("适用类型:"))
        type_combo = QComboBox()
        type_combo.addItems(["💰 工资表", "🔄 异动表", "📊 通用表"])
        type_combo.setCurrentText(self.current_table_type)
        type_layout.addWidget(type_combo)
        layout.addLayout(type_layout)

        # 模板作用域
        scope_layout = QHBoxLayout()
        scope_layout.addWidget(QLabel("作用域:"))
        scope_combo = QComboBox()
        scope_combo.addItems(["个人模板", "团队共享", "全局模板"])
        scope_layout.addWidget(scope_combo)
        layout.addLayout(scope_layout)

        # 字段预览
        preview_label = QLabel(f"将保存 {len(self.mapping_config)} 个字段映射:")
        preview_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        layout.addWidget(preview_label)

        preview_text = QTextEdit()
        preview_content = []
        for excel_field, config in list(self.mapping_config.items())[:5]:  # 只显示前5个
            target = config.get('target_field', excel_field)
            data_type = config.get('data_type', 'VARCHAR(100)')
            preview_content.append(f"• {excel_field} → {target} ({data_type})")

        if len(self.mapping_config) > 5:
            preview_content.append(f"... 还有 {len(self.mapping_config) - 5} 个字段")

        preview_text.setPlainText('\n'.join(preview_content))
        preview_text.setMaximumHeight(100)
        preview_text.setReadOnly(True)
        layout.addWidget(preview_text)

        # 按钮
        button_layout = QHBoxLayout()
        save_btn = QPushButton("💾 保存模板")
        cancel_btn = QPushButton("❌ 取消")

        button_layout.addWidget(save_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

        # 连接信号
        save_btn.clicked.connect(dialog.accept)
        cancel_btn.clicked.connect(dialog.reject)

        if dialog.exec_() == QDialog.Accepted:
            template_name = name_edit.text().strip()
            if not template_name:
                self.status_label.setText("请输入模板名称")
                return

            try:
                # 创建增强模板
                template_data = {
                    'name': template_name,
                    'description': desc_edit.toPlainText().strip(),
                    'table_type': type_combo.currentText(),
                    'scope': scope_combo.currentText(),
                    'mapping_config': self.mapping_config,
                    'field_count': len(self.mapping_config),
                    'created_by': 'current_user',  # TODO: 获取当前用户
                    'version': '1.0'
                }

                # 保存模板
                if self.template_manager.save_enhanced_template(template_data):
                    self.status_label.setText(f"模板 '{template_name}' 保存成功")
                    self.logger.info(f"增强模板保存成功: {template_name}")

                    # 保存用户映射用于学习
                    self.mapping_engine.save_user_mapping(self.current_table_type, self.mapping_config)
                else:
                    self.status_label.setText("模板保存失败")

            except Exception as e:
                error_msg = f"保存模板失败: {e}"
                self.status_label.setText(error_msg)
                self.logger.error(error_msg)

    def _load_from_template(self):
        """从模板加载"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QListWidget, QPushButton, QHBoxLayout

        # 创建模板选择对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("选择模板")
        dialog.setFixedSize(400, 300)

        layout = QVBoxLayout(dialog)

        # 模板列表
        template_list = QListWidget()
        templates = self.template_manager.get_all_templates()

        for template in templates:
            item_text = f"{template.name} ({template.type.value})"
            template_list.addItem(item_text)
            template_list.item(template_list.count() - 1).setData(Qt.UserRole, template)

        layout.addWidget(template_list)

        # 按钮
        button_layout = QHBoxLayout()
        load_btn = QPushButton("加载")
        cancel_btn = QPushButton("取消")

        button_layout.addWidget(load_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)

        # 连接信号
        load_btn.clicked.connect(dialog.accept)
        cancel_btn.clicked.connect(dialog.reject)

        if dialog.exec_() == QDialog.Accepted:
            current_item = template_list.currentItem()
            if current_item:
                template = current_item.data(Qt.UserRole)
                self._apply_template(template)

    def _apply_template(self, template):
        """应用模板"""
        try:
            # 检查是否为增强模板（包含mapping_config）
            if hasattr(template, 'mapping_config') and template.mapping_config:
                self._apply_enhanced_template(template)
            else:
                # 传统模板应用方式
                self._apply_legacy_template(template)

            self._update_mapping_config()
            self.status_label.setText(f"模板 '{template.name}' 应用成功")
            self.logger.info(f"模板应用成功: {template.name}")

        except Exception as e:
            error_msg = f"应用模板失败: {e}"
            self.status_label.setText(error_msg)
            self.logger.error(error_msg)

    def _apply_enhanced_template(self, template):
        """应用增强模板（包含字段类型信息）"""
        mapping_config = template.mapping_config

        for row in range(self.mapping_table.rowCount()):
            excel_field = self.mapping_table.item(row, 0).text()

            # 在映射配置中查找匹配的字段
            if excel_field in mapping_config:
                field_config = mapping_config[excel_field]

                # 应用数据库字段
                db_field_item = self.mapping_table.item(row, 1)
                if db_field_item and 'target_field' in field_config:
                    db_field_item.setText(field_config['target_field'])

                # 应用显示名称
                display_item = self.mapping_table.item(row, 2)
                if display_item and 'display_name' in field_config:
                    display_item.setText(field_config['display_name'])

                # 应用字段类型（第3列）
                field_type_combo = self.mapping_table.cellWidget(row, 3)
                if field_type_combo and 'field_type' in field_config:
                    index = field_type_combo.findData(field_config['field_type'])
                    if index >= 0:
                        field_type_combo.setCurrentIndex(index)

                # 应用数据类型（第4列）
                data_type_combo = self.mapping_table.cellWidget(row, 4)
                if data_type_combo and 'data_type' in field_config:
                    data_type_combo.setCurrentText(field_config['data_type'])

                # 应用是否必需（第5列）
                required_item = self.mapping_table.item(row, 5)
                if required_item and 'is_required' in field_config:
                    required_item.setCheckState(Qt.Checked if field_config['is_required'] else Qt.Unchecked)

    def _apply_legacy_template(self, template):
        """应用传统模板（兼容旧格式）"""
        for row in range(self.mapping_table.rowCount()):
            excel_field = self.mapping_table.item(row, 0).text()

            # 在模板中查找匹配的字段
            matching_field = None
            for field in template.fields:
                if field.field_name == excel_field or field.display_name == excel_field:
                    matching_field = field
                    break

            if matching_field:
                # 应用模板字段配置
                db_field_item = self.mapping_table.item(row, 1)
                if db_field_item:
                    db_field_item.setText(matching_field.field_name)

                display_item = self.mapping_table.item(row, 2)
                if display_item:
                    display_item.setText(matching_field.display_name)

                # 应用字段类型（第3列）- 传统模板可能没有字段类型信息
                field_type_combo = self.mapping_table.cellWidget(row, 3)
                if field_type_combo and hasattr(matching_field, 'field_type'):
                    index = field_type_combo.findData(matching_field.field_type)
                    if index >= 0:
                        field_type_combo.setCurrentIndex(index)

                # 应用数据类型（第4列）
                data_type_combo = self.mapping_table.cellWidget(row, 4)
                if data_type_combo:
                    data_type_combo.setCurrentText(matching_field.data_type)

                # 应用是否必需（第5列）
                required_item = self.mapping_table.item(row, 5)
                if required_item:
                    required_item.setCheckState(Qt.Checked if matching_field.is_required else Qt.Unchecked)

    def _reset_mapping(self):
        """重置映射"""
        if self.mapping_config:
            from PyQt5.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self, "确认重置",
                "确定要重置所有映射配置吗？此操作不可撤销。",
                QMessageBox.Yes | QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.load_excel_headers(self.excel_headers, self.current_table_type)
                self.status_label.setText("✅ 映射配置已重置")
                self.status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.status_label.setText("⚠️ 没有可重置的映射配置")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")

    def _validate_mapping(self):
        """验证映射配置"""
        if not self.mapping_config:
            self.status_label.setText("⚠️ 请先配置字段映射")
            self.status_label.setStyleSheet("color: orange; font-weight: bold;")
            return

        try:
            self.status_label.setText("正在验证映射配置...")

            # 使用验证引擎验证
            report = self.validation_engine.validate_import_configuration(
                self.mapping_config, self.current_table_type
            )

            # 更新验证状态显示
            if report.is_valid:
                self.validation_label.setText("✅ 验证通过")
                self.validation_label.setStyleSheet("color: green; font-weight: bold;")
                self.validation_completed.emit(True)
            else:
                self.validation_label.setText(f"❌ {report.error_count} 个错误")
                self.validation_label.setStyleSheet("color: red; font-weight: bold;")
                self.validation_completed.emit(False)

            # 显示详细信息
            details = f"验证完成: {report.summary}"
            if report.errors:
                details += f"\n错误: {'; '.join(report.errors[:3])}"
            if report.warnings:
                details += f"\n警告: {'; '.join(report.warnings[:3])}"

            self.validation_label.setToolTip(details)
            self.status_label.setText(details.split('\n')[0])

            self.logger.info(f"验证完成: {report.summary}")

        except Exception as e:
            error_msg = f"验证失败: {e}"
            self.status_label.setText(error_msg)
            self.validation_label.setText("❌ 验证失败")
            self.validation_label.setStyleSheet("color: red; font-weight: bold;")
            self.logger.error(error_msg)

    def _on_mapping_changed(self):
        """映射配置变化处理"""
        self._update_mapping_config()
        self.mapping_changed.emit()



    def _create_field_type_combo(self, current_type=None):
        """🔧 [方案一实施] 创建字段类型下拉框 - 只使用内置类型"""
        combo = QComboBox()

        # 设置统一的表格下拉框样式
        self._setup_table_combo_style(combo)

        # 🔧 [方案一实施] 只从formatting_engine获取内置类型，避免临时类型污染
        try:
            from src.modules.data_import.formatting_engine import get_formatting_engine
            formatting_engine = get_formatting_engine()
            
            # 🔧 [方案一实施] 使用新的方法获取内置类型
            if hasattr(formatting_engine, 'get_builtin_field_types'):
                builtin_types = formatting_engine.get_builtin_field_types()
                self.logger.info(f"🔧 [方案一实施] 获取内置字段类型: {len(builtin_types)} 个")
            else:
                # 兼容性：如果没有新方法，使用旧方法但过滤类型
                all_types = formatting_engine.get_field_types()
                builtin_types = {}
                for type_id, type_info in all_types.items():
                    # 过滤掉可能的临时类型（包含中文、年份等的类型）
                    if not self._is_likely_temporary_type(type_id):
                        builtin_types[type_id] = type_info
                self.logger.info(f"🔧 [方案一实施] 兼容性获取字段类型: {len(builtin_types)} 个（已过滤）")
            
            # 添加内置类型
            for type_id, type_info in builtin_types.items():
                display_name = type_info.get("name", type_id)
                combo.addItem(display_name, type_id)
                self.logger.debug(f"🔧 [方案一实施] 添加字段类型: {display_name} ({type_id})")
                
        except Exception as e:
            self.logger.warning(f"🔧 [方案一实施] 加载内置字段类型失败: {e}")
            # 回退到默认类型
            self._add_fallback_types(combo)

        # 🔧 [方案一实施] 添加自定义类型（只添加真正的自定义类型）
        try:
            custom_types_list = self.field_type_manager.list_custom_field_types()
            if custom_types_list:
                combo.insertSeparator(combo.count())
                for type_info in custom_types_list:
                    # 🔧 [方案一实施] 验证自定义类型名称格式
                    if self._is_valid_custom_type_name(type_info['id']):
                        combo.addItem(type_info['name'], type_info['id'])
                        self.logger.debug(f"🔧 [方案一实施] 添加自定义字段类型: {type_info['name']} ({type_info['id']})")
                    else:
                        self.logger.warning(f"🔧 [方案一实施] 跳过无效的自定义类型名称: {type_info['id']}")
        except Exception as e:
            self.logger.warning(f"🔧 [方案一实施] 加载自定义字段类型失败: {e}")

        # 🧠 [智能推断] 设置默认选择的字段类型
        if current_type:
            # 尝试映射旧类型ID到新类型ID
            try:
                from src.modules.data_import.formatting_engine import get_formatting_engine
                formatting_engine = get_formatting_engine()
                mapped_type = formatting_engine.get_mapped_type_id(current_type)
                
                index = combo.findData(mapped_type)
                if index >= 0:
                    combo.setCurrentIndex(index)
                    self.logger.debug(f"🧠 [智能推断] 设置字段类型: {current_type} -> {mapped_type}")
                else:
                    # 如果映射后的类型也找不到，尝试原始类型
                    index = combo.findData(current_type)
                    if index >= 0:
                        combo.setCurrentIndex(index)
                        self.logger.debug(f"🧠 [智能推断] 设置字段类型（原始）: {current_type}")
                    else:
                        self.logger.warning(f"🧠 [智能推断] 未找到字段类型: {current_type}，使用默认第一个")
            except Exception as e:
                self.logger.warning(f"🧠 [智能推断] 设置字段类型失败: {e}")
        else:
            # 🧠 [智能推断] 如果没有指定类型，默认选择第一个（通常是文本类型）
            if combo.count() > 0:
                combo.setCurrentIndex(0)
                self.logger.debug(f"🧠 [智能推断] 使用默认字段类型（第一个选项）")

        return combo

        # 🧠 [智能推断] 设置默认选择的字段类型
        if current_type:
            # 尝试映射旧类型ID到新类型ID
            try:
                from src.modules.data_import.formatting_engine import get_formatting_engine
                formatting_engine = get_formatting_engine()
                mapped_type = formatting_engine.get_mapped_type_id(current_type)
                
                index = combo.findData(mapped_type)
                if index >= 0:
                    combo.setCurrentIndex(index)
                    self.logger.debug(f"🧠 [智能推断] 设置字段类型: {current_type} -> {mapped_type}")
                else:
                    # 如果映射后的类型也找不到，尝试原始类型
                    index = combo.findData(current_type)
                    if index >= 0:
                        combo.setCurrentIndex(index)
                        self.logger.debug(f"🧠 [智能推断] 设置字段类型（原始）: {current_type}")
                    else:
                        self.logger.warning(f"🧠 [智能推断] 未找到字段类型: {current_type}，使用默认第一个")
            except Exception as e:
                self.logger.warning(f"🧠 [智能推断] 设置字段类型失败: {e}")
        else:
            # 🧠 [智能推断] 如果没有指定类型，默认选择第一个（通常是文本类型）
            if combo.count() > 0:
                combo.setCurrentIndex(0)
                self.logger.debug(f"🧠 [智能推断] 使用默认字段类型（第一个选项）")

        return combo
    
    def _is_likely_temporary_type(self, type_id: str) -> bool:
        """
        🔧 [方案一实施] 判断是否为可能的临时类型
        
        Args:
            type_id: 类型标识
            
        Returns:
            是否为可能的临时类型
        """
        if not type_id:
            return True
        
        # 检查是否包含中文字符
        if any('\u4e00' <= char <= '\u9fff' for char in type_id):
            return True
        
        # 检查是否包含4位数字（可能是年份）
        if re.search(r'\d{4}', type_id):
            return True
        
        # 检查是否为常见的数据库字段名格式
        if re.match(r'^[A-Z][a-z]+', type_id):  # 驼峰命名
            return True
        
        if re.match(r'^\d+[A-Za-z]+', type_id):  # 数字开头
            return True
        
        if re.match(r'^[A-Za-z]+\d{4}', type_id):  # 字母+4位数字
            return True
        
        return False
    
    def _is_valid_custom_type_name(self, type_id: str) -> bool:
        """
        🔧 [方案一实施] 验证自定义类型名称是否有效
        
        Args:
            type_id: 类型标识
            
        Returns:
            是否为有效的自定义类型名称
        """
        if not type_id:
            return False
        
        # 只允许字母、数字、下划线，且不能以数字开头
        if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', type_id):
            return False
        
        # 不能包含中文字符
        if any('\u4e00' <= char <= '\u9fff' for char in type_id):
            return False
        
        # 不能包含4位数字（可能是年份）
        if re.search(r'\d{4}', type_id):
            return False
        
        return True

    def _add_fallback_types(self, combo):
        """添加回退的默认类型"""
        fallback_types = [
            ("文本字符串", "text_string"),
            ("工资金额", "salary_float"),
            ("员工编号", "employee_id_string"),
            ("姓名", "name_string"),
            ("日期", "date_string"),
            ("代码", "code_string")
        ]
        
        for display_name, type_id in fallback_types:
            combo.addItem(display_name, type_id)

    def _on_field_type_changed(self):
        """🔧 [方案B实施] 字段类型变化处理 - 增强版本控制版本"""
        try:
            sender = self.sender()
            if not sender:
                return

            # 找到发送信号的下拉框所在的行
            target_row = -1
            selected_type = None
            
            for row in range(self.mapping_table.rowCount()):
                field_type_combo = self.mapping_table.cellWidget(row, 3)
                if field_type_combo == sender:
                    target_row = row
                    selected_type = field_type_combo.currentData()
                    break

            if target_row == -1 or not selected_type:
                self.logger.warning("🔧 [方案B] 无法确定字段类型变更的行或类型")
                return

            # 🔧 [方案B] 获取字段信息
            excel_item = self.mapping_table.item(target_row, 0)
            if not excel_item:
                self.logger.warning(f"🔧 [方案B] 第{target_row}行Excel字段项为空")
                return
            
            excel_field = excel_item.text()
            
            # 🔧 [方案B] 立即更新内存配置
            self._update_field_type_in_memory(target_row, selected_type)
            
            # 🔧 [方案B] 立即保存到文件，标记为用户修改
            success = self._save_field_type_immediately_with_version_control(target_row, selected_type, excel_field)
            
            if success:
                self.logger.info(f"🔧 [方案B] 字段类型已立即保存: {excel_field} -> {selected_type}")
                # 标记为已保存状态
                self._mark_field_saved(target_row)
                
                # 根据字段类型自动推荐数据类型
                recommended_data_type = self._get_recommended_data_type(selected_type)
                if recommended_data_type:
                    data_type_combo = self.mapping_table.cellWidget(target_row, 4)
                    if data_type_combo:
                        # 查找推荐的数据类型是否在下拉框中
                        index = data_type_combo.findText(recommended_data_type)
                        if index >= 0:
                            data_type_combo.setCurrentIndex(index)
                            self.logger.debug(f"🔧 [方案B] 已自动设置推荐数据类型: {recommended_data_type}")
            else:
                self.logger.error(f"🔧 [方案B] 字段类型保存失败: {excel_field} -> {selected_type}")
                # 回滚到之前的状态
                self._rollback_field_type(target_row)

            # 触发映射配置更新
            self._update_mapping_config()

            # 发送配置变化信号
            self.mapping_changed.emit()

            # 【关键修复】刷新预览验证中的格式化效果
            parent_window = self.parent()
            while parent_window and not hasattr(parent_window, 'preview_tab'):
                parent_window = parent_window.parent()

            if parent_window and hasattr(parent_window.preview_tab, 'refresh_preview_with_formatting'):
                parent_window.preview_tab.refresh_preview_with_formatting()
                self.logger.info("🔧 [方案B] 字段类型变更，已刷新预览格式化")

        except Exception as e:
            self.logger.error(f"🔧 [方案B] 字段类型变化处理失败: {e}")
            # 确保异常情况下也能回滚
            try:
                if target_row >= 0:
                    self._rollback_field_type(target_row)
            except:
                pass

    def _update_field_type_in_memory(self, row: int, field_type: str):
        """🔧 [方案一实施] 立即更新内存中的字段类型配置"""
        try:
            excel_item = self.mapping_table.item(row, 0)
            if not excel_item:
                self.logger.warning(f"第{row}行Excel字段项为空")
                return

            excel_field = excel_item.text()
            
            # 更新内存配置
            if excel_field not in self.mapping_config:
                self.mapping_config[excel_field] = {}
            
            self.mapping_config[excel_field]['field_type'] = field_type
            self.mapping_config[excel_field]['last_modified'] = self._get_current_timestamp()
            
            self.logger.debug(f"内存配置已更新: {excel_field} -> {field_type}")
            
        except Exception as e:
            self.logger.error(f"更新内存配置失败: {e}")

    def _save_field_type_immediately(self, row: int, field_type: str) -> bool:
        """🔧 [方案B实施] 立即保存字段类型到文件 - 增强版本控制版本"""
        try:
            excel_item = self.mapping_table.item(row, 0)
            if not excel_item:
                self.logger.warning(f"🔧 [方案B] 第{row}行Excel字段项为空")
                return False

            excel_field = excel_item.text()
            
            # 🔧 [方案B] 准备增强的字段配置，包含版本控制信息
            field_config = {
                'target_field': excel_field,  # 默认目标字段
                'field_type': field_type,
                'data_type': '',
                'is_required': False,
                'last_modified': self._get_current_timestamp(),
                'user_modified': True,  # 🔧 [方案B] 明确标记为用户修改
                'config_source': 'user_modified',  # 🔧 [方案B] 配置来源标记
                'modification_timestamp': time.time()  # 🔧 [方案B] 修改时间戳
            }

            # 获取其他字段信息
            target_item = self.mapping_table.item(row, 1)
            if target_item and target_item.text().strip():
                field_config['target_field'] = target_item.text().strip()

            data_type_combo = self.mapping_table.cellWidget(row, 4)
            if data_type_combo:
                field_config['data_type'] = data_type_combo.currentText() or ''

            required_item = self.mapping_table.item(row, 5)
            if required_item:
                field_config['is_required'] = required_item.checkState() == Qt.Checked

            # 🔧 [方案B] 使用ConfigSyncManager保存，应用版本控制
            if hasattr(self, 'config_sync_manager') and self.config_sync_manager:
                table_name = self._generate_table_name()
                
                success = self.config_sync_manager.save_field_mapping(
                    table_name=table_name,
                    excel_field=excel_field,
                    field_config=field_config
                )
                
                if success:
                    self.logger.info(f"🔧 [方案B] 字段类型已立即保存: {excel_field} -> {field_type}")
                    
                    # 🔧 [方案B] 更新字段状态跟踪
                    self._update_field_state_tracking(excel_field, field_config)
                    
                    return True
                else:
                    self.logger.error(f"🔧 [方案B] 字段类型保存失败: {excel_field}")
                    return False
            else:
                self.logger.warning("🔧 [方案B] ConfigSyncManager未初始化，无法保存")
                return False
                
        except Exception as e:
            self.logger.error(f"🔧 [方案B] 立即保存字段类型失败: {e}")
            return False

    def _mark_field_saved(self, row: int):
        """🔧 [方案一实施] 标记字段为已保存状态"""
        try:
            excel_item = self.mapping_table.item(row, 0)
            if not excel_item:
                return

            excel_field = excel_item.text()
            
            # 在内存配置中标记为已保存
            if excel_field in self.mapping_config:
                self.mapping_config[excel_field]['is_saved'] = True
                self.mapping_config[excel_field]['saved_timestamp'] = self._get_current_timestamp()
                
            self.logger.debug(f"字段已标记为已保存: {excel_field}")
            
        except Exception as e:
            self.logger.error(f"标记字段保存状态失败: {e}")

    def _rollback_field_type(self, row: int):
        """🔧 [方案一实施] 回滚字段类型到之前的状态"""
        try:
            excel_item = self.mapping_table.item(row, 0)
            if not excel_item:
                return

            excel_field = excel_item.text()
            
            # 从内存配置中获取之前保存的类型
            if excel_field in self.mapping_config:
                previous_type = self.mapping_config[excel_field].get('field_type', '')
                
                # 回滚UI状态
                field_type_combo = self.mapping_table.cellWidget(row, 3)
                if field_type_combo:
                    index = field_type_combo.findData(previous_type)
                    if index >= 0:
                        field_type_combo.setCurrentIndex(index)
                        self.logger.info(f"字段类型已回滚: {excel_field} -> {previous_type}")
                    else:
                        self.logger.warning(f"无法找到之前的字段类型: {previous_type}")
                        
        except Exception as e:
            self.logger.error(f"回滚字段类型失败: {e}")

    def _get_current_timestamp(self) -> float:
        """🔧 [方案一实施] 获取当前时间戳"""
        import time
        return time.time()

    def _get_recommended_field_type(self, field_name: str) -> str:
        """根据字段名推荐字段类型"""
        field_name_lower = field_name.lower()
        field_name_original = field_name.strip()

        # 🧠 [智能推断] 精确匹配优先（处理特殊字段）
        if field_name_original in ['工号', 'employee_id', '员工编号', '职工编号']:
            return "employee_id_string"
        elif field_name_original in ['人员类别代码', 'personnel_category_code', '类别代码']:
            return "personnel_category_code"
        elif field_name_original in ['姓名', 'name', '名字']:
            return "name_string"
        elif field_name_original in ['身份证号', '身份证', 'id_number']:
            return "id_number_string"
        
        # 🧠 [智能推断] 模糊匹配（包含关键词）
        elif any(keyword in field_name_lower for keyword in ['工号', '编号', 'employee', 'emp_id']):
            return "employee_id_string"
        elif any(keyword in field_name_lower for keyword in ['人员类别', '类别代码', 'category', 'personnel']):
            return "personnel_category_code"
        elif any(keyword in field_name_lower for keyword in ['姓名', '名字', 'name']):
            return "name_string"
        elif any(keyword in field_name_lower for keyword in ['工资', '薪', '金额', '费', '补贴', '津贴', '奖金', '绩效', 'salary', 'wage', 'pay']):
            return "salary_float"
        elif any(keyword in field_name_lower for keyword in ['部门', '科室', '单位', 'department', 'dept']):
            return "text_string"
        elif any(keyword in field_name_lower for keyword in ['年', '月', 'year', 'month', '日期', 'date']):
            return "date_string"
        elif any(keyword in field_name_lower for keyword in ['身份证', 'id_number', 'identity']):
            return "id_number_string"
        elif any(keyword in field_name_lower for keyword in ['代码', 'code']) and '类别' not in field_name_lower:
            return "code_string"
        else:
            # 🧠 [智能推断] 默认为文本类型
            return "text_string"

    def _get_recommended_data_type(self, field_type: str) -> str:
        """根据字段类型获取推荐的数据类型"""
        # 支持新旧字段类型ID的映射关系
        field_type_mapping = {
            # 新类型ID
            "salary_float": "DECIMAL(10,2)",
            "employee_id_string": "VARCHAR(20)",
            "name_string": "VARCHAR(50)",
            "text_string": "VARCHAR(100)",
            "date_string": "VARCHAR(10)",
            "id_number_string": "VARCHAR(18)",
            "code_string": "VARCHAR(50)",
            "float": "DECIMAL(10,4)",
            "integer": "INT",
            "personnel_category_code": "VARCHAR(10)",
            # 旧类型ID（向后兼容）
            "salary_amount": "DECIMAL(10,2)",
            "employee_id": "VARCHAR(20)",
            "department": "VARCHAR(100)",
            "year_string": "VARCHAR(4)",
            "month_string": "VARCHAR(2)",
            "general": "VARCHAR(100)"
        }

        # 尝试映射旧类型ID到新类型ID
        try:
            from src.modules.data_import.formatting_engine import get_formatting_engine
            formatting_engine = get_formatting_engine()
            mapped_type = formatting_engine.get_mapped_type_id(field_type)
            return field_type_mapping.get(mapped_type, field_type_mapping.get(field_type, "VARCHAR(100)"))
        except Exception:
            return field_type_mapping.get(field_type, "VARCHAR(100)")

    def _setup_table_combo_style(self, combo: QComboBox):
        """为表格中的下拉框设置统一样式"""
        # 设置高度，减小与单元格的间隙，给下拉框更大的显示空间
        combo.setFixedHeight(33)  # 使用33px高度，在35px行高中只留出上下各1px的最小间隙
        combo.setStyleSheet("""
            QComboBox {
                padding: 6px 8px;
                border: 1px solid #ddd;
                border-radius: 3px;
                font-size: 12px;
                background-color: white;
                margin: 0px;
            }
            QComboBox:focus {
                border-color: #0078d4;
                outline: none;
            }
            QComboBox::drop-down {
                width: 20px;
                border-left: 1px solid #ddd;
                border-top-right-radius: 3px;
                border-bottom-right-radius: 3px;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMgNUw2IDhMOSA1IiBzdHJva2U9IiM2NjY2NjYiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
            }
            QComboBox QAbstractItemView {
                border: 1px solid #ddd;
                background-color: white;
                selection-background-color: #0078d4;
                selection-color: white;
                outline: none;
            }
            QComboBox QAbstractItemView::item {
                padding: 6px 8px;
                min-height: 20px;
                border: none;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #f0f8ff;
            }
        """)

    def _update_mapping_config(self):
        """更新映射配置"""
        self.mapping_config = {}

        for row in range(self.mapping_table.rowCount()):
            # 获取Excel字段名
            excel_item = self.mapping_table.item(row, 0)
            if not excel_item:
                continue
            excel_field = excel_item.text()

            # 获取数据库字段
            db_field_item = self.mapping_table.item(row, 1)
            db_field = db_field_item.text() if db_field_item else excel_field

            # 获取显示名称
            display_item = self.mapping_table.item(row, 2)
            display_name = display_item.text() if display_item else excel_field

            # 获取字段类型 (新增)
            field_type_combo = self.mapping_table.cellWidget(row, 3)
            field_type = field_type_combo.currentData() if field_type_combo else "general"

            # 获取数据类型 (列索引从3改为4)
            data_type_combo = self.mapping_table.cellWidget(row, 4)
            data_type = data_type_combo.currentText() if data_type_combo else "VARCHAR(100)"

            # 获取是否必需 (列索引从4改为5)
            required_item = self.mapping_table.item(row, 5)
            is_required = required_item.checkState() == Qt.Checked if required_item else False

            self.mapping_config[excel_field] = {
                'target_field': db_field,
                'display_name': display_name,
                'field_type': field_type,  # 新增字段类型
                'data_type': data_type,
                'is_required': is_required
            }

    def get_mapping_config(self) -> Dict[str, Dict]:
        """获取当前映射配置"""
        return self.mapping_config.copy()

    def save_current_config(self):
        """保存当前配置 - 修复选项卡切换时数据丢失问题"""
        try:
            # 更新内存中的映射配置
            self._update_mapping_config()

            # 第二步实施：调用实时保存
            self._save_mapping_config_immediately()

            # 发送配置变化信号
            self.mapping_changed.emit()

            return True

        except Exception as e:
            self.logger.error(f"保存当前配置失败: {e}")
            return False

    def _save_mapping_config_immediately(self):
        """立即保存映射配置到文件 - 第二步实施：实时保存机制"""
        try:
            if not self.config_sync_manager:
                self.logger.debug("ConfigSyncManager未初始化，跳过实时保存")
                return False

            if not self.mapping_config:
                self.logger.debug("映射配置为空，跳过保存")
                return False

            # 生成表名（基于当前上下文）
            table_name = self._generate_table_name()

            # 准备保存的映射数据
            mapping_data = {}
            for excel_field, config in self.mapping_config.items():
                db_field = config.get('db_field', excel_field)
                mapping_data[excel_field] = db_field

            # 准备元数据
            metadata = {
                "source": "real_time_save",
                "table_type": getattr(self, 'current_table_type', ''),
                "auto_generated": False,
                "user_modified": True,
                "last_modified": "field_type_change"
            }

            # 使用ConfigSyncManager保存
            success = self.config_sync_manager.save_mapping(
                table_name=table_name,
                mapping=mapping_data,
                metadata=metadata
            )

            if success:
                self.logger.info(f"字段映射实时保存成功: {table_name}")
            else:
                self.logger.warning(f"字段映射实时保存失败: {table_name}")

            return success

        except Exception as e:
            self.logger.error(f"实时保存映射配置失败: {e}")
            return False

    def _force_save_all_field_configs(self):
        """🚨 [P0-1修复] 强制保存表格中所有字段的当前配置状态 - 批量保存优化版"""
        try:
            self.logger.info("🚨 [强制保存] 开始批量保存所有字段的当前配置状态")

            if not hasattr(self, 'mapping_table') or not self.mapping_table:
                self.logger.warning("🚨 [强制保存] 映射表格未初始化")
                return False

            # 第一步：收集所有字段的完整配置
            field_configs = {}
            processed_count = 0

            for row in range(self.mapping_table.rowCount()):
                try:
                    # 获取Excel字段名
                    excel_item = self.mapping_table.item(row, 0)
                    if not excel_item:
                        continue

                    # 🔧 [P0-2修复] 清理Excel字段名，移除换行符等特殊字符
                    raw_excel_field = excel_item.text()
                    excel_field = self._clean_field_name_for_display(raw_excel_field)
                    if not excel_field:
                        continue

                    # 初始化字段配置
                    field_config = {
                        'target_field': excel_field,  # 默认目标字段
                        'field_type': '',
                        'data_type': '',
                        'is_required': False,
                        'last_modified': self._get_current_timestamp()
                    }

                    # 获取目标字段（第1列）
                    target_item = self.mapping_table.item(row, 1)
                    if target_item and target_item.text().strip():
                        # 也清理目标字段名
                        raw_target_field = target_item.text()
                        field_config['target_field'] = self._clean_field_name(raw_target_field)

                    # 获取字段类型（第3列）
                    field_type_combo = self.mapping_table.cellWidget(row, 3)
                    if field_type_combo:
                        current_index = field_type_combo.currentIndex()
                        if current_index >= 0:
                            field_type = field_type_combo.itemData(current_index)
                            if field_type is None:
                                field_type = field_type_combo.currentText()
                            field_config['field_type'] = field_type or ''

                    # 获取数据类型（第4列）
                    data_type_combo = self.mapping_table.cellWidget(row, 4)
                    if data_type_combo:
                        field_config['data_type'] = data_type_combo.currentText() or ''

                    # 获取是否必需（第5列）
                    required_item = self.mapping_table.item(row, 5)
                    if required_item:
                        field_config['is_required'] = required_item.checkState() == Qt.Checked

                    # 更新内存配置
                    self.mapping_config[excel_field] = field_config
                    field_configs[excel_field] = field_config
                    processed_count += 1

                    self.logger.debug(f"🚨 [强制保存] 收集字段配置: '{excel_field}' -> {field_config['field_type']}")

                except Exception as e:
                    self.logger.error(f"🚨 [强制保存] 收集第{row}行字段配置失败: {e}")
                    continue

            # 第二步：批量保存所有字段配置
            if field_configs and hasattr(self, 'config_sync_manager') and self.config_sync_manager:
                table_name = self._generate_table_name()
                saved_count = 0

                for excel_field, field_config in field_configs.items():
                    try:
                        success = self.config_sync_manager.save_field_mapping(
                            table_name=table_name,
                            excel_field=excel_field,
                            field_config=field_config
                        )

                        if success:
                            saved_count += 1
                        else:
                            self.logger.warning(f"🚨 [强制保存] 字段 '{excel_field}' 保存失败")

                    except Exception as e:
                        self.logger.error(f"🚨 [强制保存] 保存字段 '{excel_field}' 失败: {e}")
                        continue

                self.logger.info(f"🚨 [强制保存] 完成，成功保存 {saved_count}/{processed_count} 个字段配置")
                return saved_count > 0
            else:
                self.logger.warning("🚨 [强制保存] ConfigSyncManager未初始化或无字段配置需要保存")
                return False

        except Exception as e:
            self.logger.error(f"🚨 [强制保存] 强制保存所有字段配置失败: {e}")
            import traceback
            self.logger.error(f"🚨 [强制保存] 详细错误: {traceback.format_exc()}")
            return False
    
    def _save_single_field_config(self, excel_field: str, config_key: str, config_value: str, immediate_save: bool = True):
        """保存单个字段的配置项 - 支持即时保存和延迟保存模式

        Args:
            excel_field: Excel字段名
            config_key: 配置键名
            config_value: 配置值
            immediate_save: 是否立即保存到文件（默认True保持兼容性）
        """
        try:
            # 1. 更新内存配置
            if excel_field not in self.mapping_config:
                self.mapping_config[excel_field] = {}

            old_value = self.mapping_config[excel_field].get(config_key)
            self.mapping_config[excel_field][config_key] = config_value
            self.mapping_config[excel_field]['last_modified'] = self._get_current_timestamp()

            # 2. 根据参数决定是否立即持久化保存
            if immediate_save and hasattr(self, 'config_sync_manager') and self.config_sync_manager:
                table_name = self._generate_table_name()

                # 准备单字段保存数据
                field_config = {
                    'target_field': self.mapping_config[excel_field].get('target_field', excel_field),
                    'field_type': self.mapping_config[excel_field].get('field_type', ''),
                    'data_type': self.mapping_config[excel_field].get('data_type', ''),
                    'is_required': self.mapping_config[excel_field].get('is_required', False),
                    'last_modified': self.mapping_config[excel_field]['last_modified']
                }

                # 使用ConfigSyncManager保存单个字段
                success = self.config_sync_manager.save_field_mapping(
                    table_name=table_name,
                    excel_field=excel_field,
                    field_config=field_config
                )

                if success:
                    self.logger.info(f"💾 [即时保存] 字段配置保存成功: {table_name}.{excel_field}")
                    return True
                else:
                    self.logger.warning(f"💾 [即时保存] 字段 '{excel_field}' 保存失败")
                    return False
            else:
                # 仅更新内存，不立即保存
                self.logger.debug(f"💾 [内存更新] {excel_field}.{config_key}: {old_value} -> {config_value}")
                return True

        except Exception as e:
            self.logger.error(f"💾 [配置保存] 保存单字段配置失败: {e}")
            return False
    
    def _get_current_timestamp(self):
        """获取当前时间戳"""
        import time
        return time.time()

    def _setup_field_type_combo_immediate_save(self, combo_box, row):
        """为字段类型下拉框设置即时保存机制 - 方案一实施"""
        try:
            # 获取Excel字段名用于日志标识
            excel_item = self.mapping_table.item(row, 0)
            excel_field = excel_item.text() if excel_item else f"Row{row}"
            
            # 立即记录设置过程
            self.logger.info(f"💾 [即时保存] 开始为字段 '{excel_field}' (第{row}行) 设置即时保存机制")
            
            def on_field_type_changed_immediate(new_type_text):
                """🔧 [P1-2修复] 带防抖机制的字段类型变更处理"""
                try:
                    self.logger.debug(f"💾 [防抖保存] 触发字段类型变更: '{excel_field}' -> {new_type_text}")

                    # 获取ComboBox的data值（类型ID）
                    current_index = combo_box.currentIndex()
                    if current_index >= 0:
                        new_type_id = combo_box.itemData(current_index)
                        if new_type_id is None:
                            new_type_id = new_type_text
                    else:
                        new_type_id = new_type_text

                    # 🔧 [P1-2修复] 防抖机制：取消之前的定时器
                    field_key = f"{excel_field}_field_type"
                    if field_key in self._debounce_timers:
                        self._debounce_timers[field_key].stop()
                        self.logger.debug(f"💾 [防抖保存] 取消之前的保存定时器: {field_key}")

                    # 创建新的防抖定时器
                    def debounced_save():
                        """防抖延迟执行的保存操作"""
                        try:
                            self.logger.info(f"💾 [防抖保存] *** 执行延迟保存 *** 字段: '{excel_field}', 类型: {new_type_id}")

                            # 1. 保存字段类型配置（使用非即时保存模式避免重复）
                            save_success = self._save_single_field_config(excel_field, 'field_type', new_type_id, immediate_save=True)
                            self.logger.info(f"💾 [防抖保存] 保存结果: {'成功' if save_success else '失败'}")

                            # 2. 更新格式化引擎
                            try:
                                from src.modules.data_import.formatting_engine import get_formatting_engine
                                formatting_engine = get_formatting_engine()
                                formatting_engine.register_field_type(excel_field, {'type': new_type_id})
                                self.logger.debug(f"💾 [防抖保存] 格式化引擎更新成功: {excel_field} -> {new_type_id}")
                            except Exception as fmt_e:
                                self.logger.warning(f"💾 [防抖保存] 更新格式化引擎失败: {fmt_e}")

                            # 3. 触发映射配置变化信号
                            if hasattr(self, 'mapping_changed'):
                                self.mapping_changed.emit()
                                self.logger.debug(f"💾 [防抖保存] 已触发mapping_changed信号")

                            # 4. 状态反馈
                            if save_success:
                                if hasattr(self, 'status_label'):
                                    self.status_label.setText(f"✅ 已保存 '{excel_field}' 字段类型")
                                    self.status_label.setStyleSheet("color: #28a745; font-weight: normal;")
                                    # 2秒后清除状态提示
                                    QTimer.singleShot(2000, lambda: self.status_label.setText("就绪"))

                            # 清理定时器引用
                            if field_key in self._debounce_timers:
                                del self._debounce_timers[field_key]

                        except Exception as save_e:
                            self.logger.error(f"💾 [防抖保存] 延迟保存执行失败: {save_e}")

                    # 创建并启动防抖定时器
                    timer = QTimer()
                    timer.setSingleShot(True)
                    timer.timeout.connect(debounced_save)
                    timer.start(self._debounce_delay)

                    # 保存定时器引用
                    self._debounce_timers[field_key] = timer
                    self.logger.debug(f"💾 [防抖保存] 启动防抖定时器: {field_key}, 延迟: {self._debounce_delay}ms")
                    
                except Exception as e:
                    self.logger.error(f"💾 [即时保存] 字段类型变更处理失败: {e}")
                    import traceback
                    self.logger.error(f"💾 [即时保存] 详细错误: {traceback.format_exc()}")
                    
                    # 错误反馈
                    if hasattr(self, 'status_label'):
                        self.status_label.setText(f"❌ 保存失败: {excel_field}")
                        self.status_label.setStyleSheet("color: #dc3545; font-weight: normal;")
            
            # 验证ComboBox有效性
            if combo_box is None:
                self.logger.error(f"💾 [即时保存] ComboBox为None，无法连接信号")
                return
                
            # 连接信号到即时保存处理函数
            combo_box.currentTextChanged.connect(on_field_type_changed_immediate)
            self.logger.info(f"💾 [即时保存] ✅ 信号连接成功: '{excel_field}' currentTextChanged -> 即时保存处理函数")
            
            # 验证信号连接是否成功（通过检查连接数量）
            try:
                signal_info = combo_box.currentTextChanged
                self.logger.info(f"💾 [即时保存] 信号对象验证: {type(signal_info)}")
            except Exception as verify_e:
                self.logger.warning(f"💾 [即时保存] 信号验证失败: {verify_e}")
            
        except Exception as e:
            self.logger.error(f"💾 [即时保存] 设置即时保存机制失败: {e}")
            import traceback
            self.logger.error(f"💾 [即时保存] 详细错误: {traceback.format_exc()}")

    def _generate_table_name(self):
        """生成用于保存字段映射配置的表名（稳定版本，不使用时间戳）"""
        try:
            current_sheet = self._get_current_sheet_name()
            
            if hasattr(self, 'parent') and self.parent():
                parent_window = self.parent()
                if hasattr(parent_window, 'current_file_path') and parent_window.current_file_path:
                    import os, hashlib
                    # 使用文件路径哈希确保唯一性，但保持稳定
                    file_hash = hashlib.md5(parent_window.current_file_path.encode('utf-8')).hexdigest()[:8]
                    table_name = f"mapping_config_{file_hash}_{current_sheet}"
                    self.logger.info(f"💾 [表名生成] 生成稳定表名: {table_name} (文件: {os.path.basename(parent_window.current_file_path)})")
                    return table_name
            
            # 备用方案：仅使用Sheet名称
            table_name = f"mapping_config_{current_sheet}"
            self.logger.info(f"💾 [表名生成] 备用稳定表名: {table_name}")
            return table_name
            
        except Exception as e:
            self.logger.error(f"💾 [表名生成] 生成表名失败: {e}")
            return f"mapping_config_default_{self._get_current_sheet_name()}"


    def _get_current_sheet_name(self):
        """获取当前Sheet名称"""
        try:
            # 优先使用缓存的Sheet名称
            if hasattr(self, '_current_sheet_name') and self._current_sheet_name:
                self.logger.debug(f"💾 [Sheet名称] 使用缓存: {self._current_sheet_name}")
                return self._current_sheet_name
            
            # 从父窗口的sheet管理组件获取
            if hasattr(self, 'parent') and self.parent():
                parent_window = self.parent()
                if hasattr(parent_window, 'sheet_management_widget'):
                    sheet_widget = parent_window.sheet_management_widget
                    if hasattr(sheet_widget, 'sheet_config_manager') and sheet_widget.sheet_config_manager:
                        current_sheet = sheet_widget.sheet_config_manager.current_sheet
                        if current_sheet:
                            self.logger.debug(f"💾 [Sheet名称] 从配置管理器获取: {current_sheet}")
                            return current_sheet
                    
                    # 从Sheet树获取当前选中项
                    if hasattr(sheet_widget, 'sheet_tree') and sheet_widget.sheet_tree:
                        current_item = sheet_widget.sheet_tree.currentItem()
                        if current_item:
                            sheet_name = current_item.text(0)
                            self.logger.debug(f"💾 [Sheet名称] 从树控件获取: {sheet_name}")
                            return sheet_name
            
            # 默认值
            self.logger.warning("💾 [Sheet名称] 无法获取当前Sheet，使用默认值")
            return "default_sheet"
            
        except Exception as e:
            self.logger.error(f"获取当前Sheet名称失败: {e}")
            return "error_sheet"

    def _on_advanced_config_changed(self, config: Dict[str, Any]):
        """高级配置变化处理"""
        try:
            # 应用智能推荐设置
            smart_config = config.get('smart_recommendations', {})

            # 更新智能映射引擎配置
            if hasattr(self.mapping_engine, 'update_config'):
                self.mapping_engine.update_config(smart_config)

            # 更新置信度阈值
            confidence_threshold = smart_config.get('confidence_threshold', 70) / 100.0

            # 如果启用了自动应用高置信度推荐
            if smart_config.get('auto_apply_high_confidence', False):
                self._auto_apply_high_confidence_mappings(confidence_threshold)

            # 应用UI设置
            ui_config = config.get('ui_customization', {})

            # 更新表格行数限制
            row_limit = ui_config.get('table_row_limit', 200)
            if hasattr(self, 'mapping_table'):
                # TODO: 应用行数限制到表格显示
                pass

            # 显示/隐藏置信度指示器
            show_confidence = ui_config.get('show_confidence_indicators', True)
            self._toggle_confidence_indicators(show_confidence)

            self.logger.info("高级配置已应用到映射组件")

        except Exception as e:
            self.logger.error(f"应用高级配置失败: {e}")

    def _auto_apply_high_confidence_mappings(self, threshold: float):
        """自动应用高置信度映射"""
        if not self.excel_headers:
            return

        try:
            # 重新生成智能映射
            mapping_results = self.mapping_engine.generate_smart_mapping(
                self.excel_headers, self.current_table_type
            )

            applied_count = 0
            for result in mapping_results:
                if result.confidence >= threshold:
                    row = self.excel_headers.index(result.source_field)

                    # 自动应用高置信度映射
                    db_field_item = self.mapping_table.item(row, 1)
                    if db_field_item:
                        db_field_item.setText(result.target_field)
                        applied_count += 1

            if applied_count > 0:
                self.status_label.setText(f"自动应用了 {applied_count} 个高置信度映射")
                self._update_mapping_config()

        except Exception as e:
            self.logger.error(f"自动应用高置信度映射失败: {e}")

    def _toggle_confidence_indicators(self, show: bool):
        """切换置信度指示器显示"""
        if not hasattr(self, 'mapping_table'):
            return

        try:
            # 显示/隐藏验证状态列
            column_index = 5  # 验证状态列
            if show:
                self.mapping_table.showColumn(column_index)
            else:
                self.mapping_table.hideColumn(column_index)

        except Exception as e:
            self.logger.error(f"切换置信度指示器失败: {e}")

    def update_for_sheet(self, sheet_name: str, sheet_config):
        """
        为指定Sheet更新字段映射配置

        Args:
            sheet_name: Sheet名称
            sheet_config: Sheet配置对象
        """
        try:
            self.logger.info(f"更新字段映射配置: {sheet_name}")

            # 🔧 [关键修复] 缓存当前Sheet名称，用于表名生成
            self._current_sheet_name = sheet_name

            # 获取父窗口的文件路径和导入管理器
            parent_window = self.parent()
            while parent_window and not hasattr(parent_window, 'current_file_path'):
                parent_window = parent_window.parent()

            if not parent_window or not hasattr(parent_window, 'current_file_path'):
                self.logger.warning("无法获取父窗口或文件路径")
                return

            current_file_path = parent_window.current_file_path
            if not current_file_path:
                self.logger.warning("当前没有选择Excel文件")
                return

            # 从Excel文件中动态获取Sheet的字段信息
            if hasattr(parent_window, 'import_manager') and parent_window.import_manager:
                df = parent_window.import_manager.excel_importer.import_data(
                    current_file_path, sheet_name, max_rows=1
                )

                if df is not None and not df.empty:
                    headers = list(df.columns)
                    self.logger.info(f"从Excel文件获取字段: {len(headers)} 个")
                else:
                    self.logger.warning(f"Sheet '{sheet_name}' 数据为空或无法读取")
                    return
            else:
                self.logger.warning("无法获取导入管理器")
                return

            # 获取表类型
            table_type = getattr(sheet_config, 'table_type', self.current_table_type)
            if not table_type:
                table_type = getattr(parent_window, 'current_table_type', "💰 工资表")

            # 🔧 [方案A实施] 调整配置加载顺序：先加载保存的配置，再应用智能推断
            # 第一步：从ConfigSyncManager加载该Sheet的专用配置
            saved_configs = self._load_sheet_specific_config_early(sheet_name)
            
            # 第二步：加载字段到映射表格，但优先使用保存的配置
            self.load_excel_headers_with_saved_config(headers, table_type, saved_configs)
            
            # 第三步：备用：尝试加载已保存的映射配置
            if hasattr(sheet_config, 'field_mappings') and sheet_config.field_mappings:
                self._apply_saved_field_mappings(sheet_config.field_mappings)

            # 更新状态
            self.status_label.setText(f"已加载Sheet '{sheet_name}' 的字段映射")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")

        except Exception as e:
            error_msg = f"更新Sheet字段映射失败: {e}"
            self.logger.error(error_msg)
            self.status_label.setText(error_msg)
            self.status_label.setStyleSheet("color: red; font-weight: bold;")

    def _load_sheet_specific_config_early(self, sheet_name: str) -> dict:
        """🔧 [方案B实施] 早期加载指定Sheet的专用配置 - 增强版本控制版本"""
        try:
            if not hasattr(self, 'config_sync_manager') or not self.config_sync_manager:
                self.logger.warning("🔧 [方案B] ConfigSyncManager未初始化")
                return {}

            # 生成该Sheet的表名
            table_name = self._generate_table_name()
            self.logger.info(f"🔧 [方案B] 加载Sheet '{sheet_name}' 配置，表名: {table_name}")

            # 🔧 [方案B] 从ConfigSyncManager加载配置，包含版本信息
            saved_mappings = self.config_sync_manager.load_mapping(table_name)
            if saved_mappings:
                self.logger.info(f"🔧 [方案B] 找到 {len(saved_mappings)} 个已保存的字段配置")
                
                # 🔧 [方案B] 增强配置加载：应用版本控制和冲突检测
                enhanced_configs = self._enhance_configs_with_version_control(table_name, saved_mappings)
                self.logger.info(f"🔧 [方案B] 配置版本控制增强完成，有效配置: {len(enhanced_configs)} 个")
                
                return enhanced_configs
            else:
                self.logger.info(f"🔧 [方案B] Sheet '{sheet_name}' 暂无保存的配置")
                return {}

        except Exception as e:
            self.logger.error(f"🔧 [方案B] 加载Sheet配置失败: {e}")
            return {}

    def _load_sheet_specific_config(self, sheet_name: str):
        """🔧 [方案一实施] 加载指定Sheet的专用配置 - 增强版本控制"""
        try:
            if not hasattr(self, 'config_sync_manager') or not self.config_sync_manager:
                self.logger.warning("💾 [配置加载] ConfigSyncManager未初始化")
                return

            # 生成该Sheet的表名
            table_name = self._generate_table_name()
            self.logger.info(f"💾 [配置加载] 加载Sheet '{sheet_name}' 配置，表名: {table_name}")

            # 🔧 [方案一实施] 从ConfigSyncManager加载字段映射配置
            saved_mappings = self.config_sync_manager.load_mapping(table_name)
            
            if saved_mappings:
                self.logger.info(f"💾 [配置加载] 找到 {len(saved_mappings)} 个已保存的字段配置")
                
                # 🔧 [方案一实施] 进行配置冲突检测
                conflict_resolved = self._resolve_config_conflicts(saved_mappings)
                
                if conflict_resolved:
                    self.logger.info("💾 [配置加载] 配置冲突已解决，应用最新配置")
                    self._apply_saved_mapping_config(saved_mappings)
                else:
                    self.logger.warning("💾 [配置加载] 配置冲突解决失败，使用当前UI状态")
            else:
                self.logger.info(f"💾 [配置加载] Sheet '{sheet_name}' 暂无保存的配置，尝试使用SheetConfig兜底回填")
                # 兜底：从 SheetConfigManager 读取 field_types 与 field_mappings 组装最小可用配置
                try:
                    sheet_cfg = self.get_or_create_sheet_config(sheet_name)
                    field_types = getattr(sheet_cfg, 'field_types', {}) or {}
                    base_mappings = getattr(sheet_cfg, 'field_mappings', {}) or {}

                    if field_types or base_mappings:
                        fallback_configs = {}
                        for row in range(self.mapping_table.rowCount()):
                            excel_item = self.mapping_table.item(row, 0)
                            if not excel_item:
                                continue
                            excel_field = excel_item.text()
                            field_type_val = field_types.get(excel_field)
                            # 若无类型，则根据当前UI推荐类型回退
                            if not field_type_val:
                                combo = self.mapping_table.cellWidget(row, 3)
                                if combo:
                                    idx = combo.currentIndex()
                                    field_type_val = combo.itemData(idx) if idx >= 0 else None
                            # 推荐数据类型
                            data_type_val = None
                            if field_type_val:
                                try:
                                    data_type_val = self._get_recommended_data_type(field_type_val)
                                except Exception:
                                    data_type_val = None
                            # 目标字段
                            target_field_val = base_mappings.get(excel_field, excel_field)

                            fallback_configs[excel_field] = {
                                'target_field': target_field_val,
                                'field_type': field_type_val or '',
                                'data_type': data_type_val or '',
                                'is_required': False
                            }

                        if fallback_configs:
                            self.logger.info(f"💾 [兜底回填] 从SheetConfig生成 {len(fallback_configs)} 条配置，开始应用")
                            self._apply_saved_mapping_config(fallback_configs)
                        else:
                            self.logger.info("💾 [兜底回填] SheetConfig无可用信息，保持智能推断默认")
                    else:
                        self.logger.info("💾 [兜底回填] 无field_types/field_mappings可用，保持智能推断默认")
                except Exception as fe:
                    self.logger.warning(f"💾 [兜底回填] 失败: {fe}")

        except Exception as e:
            self.logger.error(f"💾 [配置加载] 加载Sheet配置失败: {e}")
            import traceback
            self.logger.error(f"💾 [配置加载] 详细错误: {traceback.format_exc()}")

    def _resolve_config_conflicts(self, saved_config: dict) -> bool:
        """🔧 [方案一实施] 解决配置冲突 - 基于时间戳的优先级判断"""
        try:
            if not saved_config:
                return True

            conflict_count = 0
            resolved_count = 0

            for row in range(self.mapping_table.rowCount()):
                excel_item = self.mapping_table.item(row, 0)
                if not excel_item:
                    continue

                excel_field = excel_item.text()
                if excel_field not in saved_config:
                    continue

                # 获取当前UI状态
                current_field_type = self._get_current_ui_field_type(row)
                saved_field_type = saved_config[excel_field].get('field_type', '')

                # 如果字段类型不同，进行冲突检测
                if current_field_type != saved_field_type:
                    conflict_count += 1
                    
                    # 获取时间戳信息
                    saved_timestamp = saved_config[excel_field].get('last_modified', 0)
                    current_timestamp = self._get_field_last_modified(row)
                    
                    if saved_timestamp > current_timestamp:
                        # 保存的配置更新，标记为需要应用
                        saved_config[excel_field]['_should_apply'] = True
                        resolved_count += 1
                        self.logger.debug(f"配置冲突解决: {excel_field} -> 使用保存的配置 (时间戳: {saved_timestamp})")
                    else:
                        # 当前UI状态更新，标记为不需要应用
                        saved_config[excel_field]['_should_apply'] = False
                        resolved_count += 1
                        self.logger.debug(f"配置冲突解决: {excel_field} -> 保持当前UI状态 (时间戳: {current_timestamp})")

            self.logger.info(f"配置冲突检测完成: 发现 {conflict_count} 个冲突，解决 {resolved_count} 个")
            return True

        except Exception as e:
            self.logger.error(f"解决配置冲突失败: {e}")
            return False

    def _get_current_ui_field_type(self, row: int) -> str:
        """🔧 [方案一实施] 获取当前UI中指定行的字段类型"""
        try:
            field_type_combo = self.mapping_table.cellWidget(row, 3)
            if field_type_combo:
                return field_type_combo.currentData() or ''
            return ''
        except Exception as e:
            self.logger.error(f"获取当前UI字段类型失败: {e}")
            return ''

    def _get_field_last_modified(self, row: int) -> float:
        """🔧 [方案一实施] 获取指定行字段的最后修改时间戳"""
        try:
            excel_item = self.mapping_table.item(row, 0)
            if not excel_item:
                return 0

            excel_field = excel_item.text()
            if excel_field in self.mapping_config:
                return self.mapping_config[excel_field].get('last_modified', 0)
            return 0
        except Exception as e:
            self.logger.error(f"获取字段最后修改时间戳失败: {e}")
            return 0

    def _apply_saved_mapping_config(self, saved_config: dict):
        """
        🔧 [方案一实施] 应用已保存的映射配置 - 智能冲突解决版本

        Args:
            saved_config: 保存的映射配置
        """
        try:
            if not saved_config:
                return

            # 临时断开信号连接
            self.mapping_table.cellChanged.disconnect()

            applied_count = 0
            skipped_count = 0

            for row in range(self.mapping_table.rowCount()):
                excel_item = self.mapping_table.item(row, 0)
                if not excel_item:
                    continue

                excel_field = excel_item.text()
                if excel_field in saved_config:
                    config = saved_config[excel_field]
                    
                    # 🔧 [方案一实施] 检查是否应该应用此配置
                    should_apply = config.get('_should_apply', True)
                    if not should_apply:
                        self.logger.debug(f"跳过配置应用: {excel_field} (保持当前UI状态)")
                        skipped_count += 1
                        continue

                    # 更新数据库字段（兼容 db_field/target_field）
                    db_field_item = self.mapping_table.item(row, 1)
                    if db_field_item:
                        target_val = config.get('target_field', config.get('db_field'))
                        if target_val:
                            db_field_item.setText(target_val)

                    # 更新显示名称（可选）
                    display_item = self.mapping_table.item(row, 2)
                    if display_item:
                        disp = config.get('display_name') or config.get('alias')
                        if disp:
                            display_item.setText(disp)

                    # 🔧 [方案一实施] 更新字段类型（第3列）- 智能冲突解决
                    field_type_combo = self.mapping_table.cellWidget(row, 3)
                    if field_type_combo and 'field_type' in config:
                        # 支持旧类型ID映射
                        field_type = config['field_type']
                        try:
                            from src.modules.data_import.formatting_engine import get_formatting_engine
                            formatting_engine = get_formatting_engine()
                            mapped_type = formatting_engine.get_mapped_type_id(field_type)
                            
                            # 尝试设置映射后的类型
                            index = field_type_combo.findData(mapped_type)
                            if index >= 0:
                                field_type_combo.setCurrentIndex(index)
                                self.logger.debug(f"已应用字段类型: {excel_field} -> {mapped_type}")
                            else:
                                # 如果映射后的类型找不到，尝试原始类型
                                index = field_type_combo.findData(field_type)
                                if index >= 0:
                                    field_type_combo.setCurrentIndex(index)
                                    self.logger.debug(f"已应用字段类型（原始）: {excel_field} -> {field_type}")
                                else:
                                    self.logger.warning(f"无法找到字段类型: {excel_field} -> {field_type}")
                        except Exception as e:
                            self.logger.warning(f"设置字段类型失败 {field_type}: {e}")
                    
                    # 更新数据类型（第4列）
                    data_type_combo = self.mapping_table.cellWidget(row, 4)
                    if data_type_combo and 'data_type' in config:
                        data_type_combo.setCurrentText(config['data_type'])

                    # 更新是否必需（第5列）
                    required_item = self.mapping_table.item(row, 5)
                    if required_item and 'is_required' in config:
                        required_item.setCheckState(Qt.Checked if config['is_required'] else Qt.Unchecked)

                    applied_count += 1

            # 重新连接信号
            self.mapping_table.cellChanged.connect(self._on_mapping_changed)

            # 更新内部配置
            self._update_mapping_config()

            self.logger.info(f"🔧 [方案一实施] 配置应用完成: 应用 {applied_count} 个，跳过 {skipped_count} 个")

        except Exception as e:
            self.logger.error(f"应用保存的映射配置失败: {e}")
            # 确保重新连接信号
            try:
                self.mapping_table.cellChanged.connect(self._on_mapping_changed)
            except:
                pass

    def _apply_saved_field_mappings(self, field_mappings: dict):
        """
        应用已保存的字段映射

        Args:
            field_mappings: 字段映射字典 {excel_field: db_field}
        """
        try:
            if not field_mappings:
                return

            # 临时断开信号连接
            self.mapping_table.cellChanged.disconnect()

            for row in range(self.mapping_table.rowCount()):
                excel_item = self.mapping_table.item(row, 0)
                if not excel_item:
                    continue

                excel_field = excel_item.text()
                if excel_field in field_mappings:
                    db_field = field_mappings[excel_field]

                    # 更新数据库字段
                    db_field_item = self.mapping_table.item(row, 1)
                    if db_field_item:
                        db_field_item.setText(db_field)

                    # 更新显示名称（使用Excel字段名作为默认显示名称）
                    display_item = self.mapping_table.item(row, 2)
                    if display_item:
                        display_item.setText(excel_field)

            # 重新连接信号
            self.mapping_table.cellChanged.connect(self._on_mapping_changed)

            # 更新内部配置
            self._update_mapping_config()

            self.logger.info("已应用保存的字段映射")

        except Exception as e:
            self.logger.error(f"应用保存的字段映射失败: {e}")
            # 确保重新连接信号
            try:
                self.mapping_table.cellChanged.connect(self._on_mapping_changed)
            except:
                pass

    def closeEvent(self, event):
        """🔧 [P0-3修复] 组件关闭时清理防抖定时器"""
        try:
            # 清理所有防抖定时器
            if hasattr(self, '_debounce_timers'):
                for field_key, timer in self._debounce_timers.items():
                    if timer.isActive():
                        timer.stop()
                        self.logger.debug(f"💾 [防抖清理] 停止定时器: {field_key}")

                self._debounce_timers.clear()
                self.logger.info("💾 [防抖清理] 所有防抖定时器已清理")

        except Exception as e:
            self.logger.error(f"💾 [防抖清理] 清理防抖定时器失败: {e}")

        # 调用父类的关闭事件
        super().closeEvent(event)




class PreviewValidationWidget(QWidget):
    """预览验证选项卡组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        self.current_sheet_name = ""
        self.current_preview_data = []  # 缓存原始预览数据
        self._init_ui()

    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # 预览信息头
        info_layout = QHBoxLayout()
        self.sheet_label = QLabel("当前工作表: 未选择")
        self.sheet_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.record_count_label = QLabel("记录数: 0")

        info_layout.addWidget(self.sheet_label)
        info_layout.addStretch()
        info_layout.addWidget(self.record_count_label)
        layout.addLayout(info_layout)

        # 预览数据表格
        self.preview_table = QTableWidget()
        self.preview_table.setAlternatingRowColors(True)
        self.preview_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.preview_table.setSelectionMode(QTableWidget.SingleSelection)
        layout.addWidget(self.preview_table, 1)

        # 验证结果区域
        validation_group = QGroupBox("验证结果")
        validation_layout = QVBoxLayout(validation_group)

        # 验证状态
        status_layout = QHBoxLayout()
        self.validation_status_label = QLabel("验证状态: 未验证")
        self.validation_status_label.setStyleSheet("font-weight: bold;")

        self.validate_data_btn = QPushButton("🔍 验证数据")
        self.validate_data_btn.setMinimumHeight(30)

        status_layout.addWidget(self.validation_status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.validate_data_btn)
        validation_layout.addLayout(status_layout)

        # 错误信息列表
        self.error_list = QTreeWidget()
        self.error_list.setHeaderLabels(["类型", "字段", "行号", "错误信息"])
        self.error_list.setMaximumHeight(150)
        validation_layout.addWidget(self.error_list)

        layout.addWidget(validation_group)

        # 连接信号
        self.validate_data_btn.clicked.connect(self._validate_current_data)

    def show_preview_data(self, sheet_name: str, data: List[Dict], field_mappings: Dict[str, Dict] = None):
        """显示预览数据（支持字段类型格式化）"""
        try:
            self.logger.info(f"🔧 [调试] 开始显示预览数据: {sheet_name}, 数据行数: {len(data) if data else 0}")
            self.logger.info(f"🔧 [调试] 字段映射配置: {field_mappings}")
            
            # 缓存原始数据
            self.current_preview_data = data.copy() if data else []
            self.current_sheet_name = sheet_name
            self.sheet_label.setText(f"当前工作表: {sheet_name}")
            self.record_count_label.setText(f"记录数: {len(data)}")

            if not data:
                self.preview_table.setRowCount(0)
                self.preview_table.setColumnCount(0)
                self.logger.info("🔧 [调试] 数据为空，清空表格")
                return

            # 获取格式化引擎
            from src.modules.data_import.formatting_engine import get_formatting_engine
            formatting_engine = get_formatting_engine()
            self.logger.info("🔧 [调试] 获取格式化引擎成功")

            # 设置表格结构
            headers = list(data[0].keys())
            self.preview_table.setColumnCount(len(headers))
            self.preview_table.setHorizontalHeaderLabels(headers)
            self.preview_table.setRowCount(len(data))
            self.logger.info(f"🔧 [调试] 设置表格结构: {len(headers)} 列, {len(data)} 行")

            # 填充数据并应用格式化
            for row, record in enumerate(data):
                for col, header in enumerate(headers):
                    value = record.get(header, "")
                    
                    # 🔧 [修复] 应用字段类型格式化，确保类型安全
                    formatted_value = ""
                    if field_mappings and header in field_mappings:
                        field_config = field_mappings[header]
                        field_type = field_config.get('field_type')
                        
                        # 🔧 [修复] 记录字段映射信息
                        if row == 0:  # 只在第一行记录，避免日志过多
                            self.logger.info(f"🔧 [修复] 字段 '{header}' 映射到类型: {field_type}")
                        
                        if field_type and field_type.strip():  # 🔧 [修复] 确保字段类型不为空
                            # 使用格式化引擎格式化值
                            try:
                                # 🔧 [修复] 确保值不为None且转换为字符串
                                str_value = str(value) if value is not None else ""
                                formatted_value = formatting_engine.format_value(str_value, field_type)
                                
                                # 🔧 [修复] 记录格式化结果
                                if row == 0 and str(value) != str(formatted_value):
                                    self.logger.info(f"🔧 [修复] 字段 '{header}' 格式化: '{value}' -> '{formatted_value}'")
                                
                                # 确保格式化结果为字符串类型
                                if not isinstance(formatted_value, str):
                                    formatted_value = str(formatted_value) if formatted_value is not None else ""
                            except Exception as fmt_err:
                                self.logger.warning(f"🔧 [修复] 格式化失败 {header} (类型: {field_type}): {fmt_err}")
                                formatted_value = str(value) if value is not None else ""
                        else:
                            formatted_value = str(value) if value is not None else ""
                            if row == 0:
                                self.logger.info(f"🔧 [修复] 字段 '{header}' 无有效字段类型，使用原值")
                    else:
                        formatted_value = str(value) if value is not None else ""
                        if row == 0:
                            self.logger.info(f"🔧 [修复] 字段 '{header}' 无映射配置，使用原值")
                    
                    # 🔧 [修复] 确保传递给QTableWidgetItem的值为字符串类型
                    item = QTableWidgetItem(str(formatted_value))
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # 只读
                    
                    # 处理特殊格式标记（如红色负数）
                    if "[RED]" in formatted_value:
                        formatted_value = formatted_value.replace("[RED]", "").replace("[/RED]", "")
                        item.setText(formatted_value)
                        item.setForeground(Qt.red)
                    
                    self.preview_table.setItem(row, col, item)

            # 自动调整列宽
            self.preview_table.resizeColumnsToContents()

            # 重置验证状态
            self.validation_status_label.setText("验证状态: 未验证")
            self.validation_status_label.setStyleSheet("font-weight: bold; color: #666;")
            self.error_list.clear()

            self.logger.info(f"预览数据加载完成（已应用格式化）: {sheet_name} - {len(data)} 行")

        except Exception as e:
            self.logger.error(f"显示预览数据失败: {e}")

    def _validate_current_data(self):
        """验证当前预览的数据"""
        try:
            if not self.current_sheet_name:
                self.validation_status_label.setText("验证状态: 无数据可验证")
                return

            self.validation_status_label.setText("验证状态: 验证中...")
            self.validation_status_label.setStyleSheet("font-weight: bold; color: #ff9800;")

            # TODO: 实现数据验证逻辑
            # 这里应该获取当前的映射配置，然后验证数据

            # 模拟验证结果
            self.error_list.clear()

            # 示例：添加一些验证结果
            from PyQt5.QtWidgets import QTreeWidgetItem

            warning_item = QTreeWidgetItem(["警告", "姓名", "3", "字段值可能包含特殊字符"])
            warning_item.setIcon(0, self.style().standardIcon(self.style().SP_MessageBoxWarning))
            self.error_list.addTopLevelItem(warning_item)

            error_item = QTreeWidgetItem(["错误", "工资", "5", "数据类型不匹配，期望数字类型"])
            error_item.setIcon(0, self.style().standardIcon(self.style().SP_MessageBoxCritical))
            self.error_list.addTopLevelItem(error_item)

            # 更新验证状态
            error_count = self.error_list.topLevelItemCount()
            if error_count == 0:
                self.validation_status_label.setText("验证状态: ✅ 通过")
                self.validation_status_label.setStyleSheet("font-weight: bold; color: green;")
            else:
                self.validation_status_label.setText(f"验证状态: ❌ 发现 {error_count} 个问题")
                self.validation_status_label.setStyleSheet("font-weight: bold; color: red;")

            self.logger.info(f"数据验证完成: {self.current_sheet_name} - {error_count} 个问题")

        except Exception as e:
            self.validation_status_label.setText("验证状态: 验证失败")
            self.validation_status_label.setStyleSheet("font-weight: bold; color: red;")
            self.logger.error(f"数据验证失败: {e}")

    def update_for_sheet(self, sheet_name: str, sheet_config):
        """
        为指定Sheet更新预览验证内容

        Args:
            sheet_name: Sheet名称
            sheet_config: Sheet配置对象
        """
        try:
            self.logger.info(f"更新预览验证内容: {sheet_name}")

            # 更新当前Sheet名称
            self.current_sheet_name = sheet_name
            self.sheet_label.setText(f"当前工作表: {sheet_name}")

            # 获取父窗口的文件路径和导入管理器
            parent_window = self.parent()
            while parent_window and not hasattr(parent_window, 'current_file_path'):
                parent_window = parent_window.parent()

            if not parent_window or not hasattr(parent_window, 'current_file_path'):
                self.logger.warning("无法获取父窗口或文件路径")
                return

            current_file_path = parent_window.current_file_path
            if not current_file_path:
                self.logger.warning("当前没有选择Excel文件")
                return

            # 从Excel文件中读取预览数据
            if hasattr(parent_window, 'import_manager') and parent_window.import_manager:
                df = parent_window.import_manager.excel_importer.import_data(
                    current_file_path, sheet_name, max_rows=100  # 预览前100行
                )

                if df is not None and not df.empty:
                    self.update_preview_data(df)
                    self.record_count_label.setText(f"记录数: {len(df)}")
                    self.logger.info(f"已加载Sheet '{sheet_name}' 的预览数据: {len(df)} 行")
                else:
                    self.logger.warning(f"Sheet '{sheet_name}' 数据为空或无法读取")
                    self.clear_preview()
            else:
                self.logger.warning("无法获取导入管理器")
                self.clear_preview()

        except Exception as e:
            error_msg = f"更新Sheet预览失败: {e}"
            self.logger.error(error_msg)
            self.clear_preview()

    def update_preview_data(self, df):
        """
        更新预览数据（接受DataFrame格式）

        Args:
            df: pandas DataFrame对象
        """
        try:
            if df is None or df.empty:
                self.logger.warning("DataFrame为空，清空预览")
                self.clear_preview()
                return

            # 将DataFrame转换为字典列表格式
            data_dicts = df.to_dict('records')

            # 🔧 [关键修复] 获取字段映射配置
            field_mappings = {}
            parent_window = self.parent()
            while parent_window and not hasattr(parent_window, 'mapping_tab'):
                parent_window = parent_window.parent()
            
            if parent_window and hasattr(parent_window.mapping_tab, 'get_current_mapping_config'):
                mapping_config = parent_window.mapping_tab.get_current_mapping_config()
                # 转换为预览需要的格式
                for excel_field, config in mapping_config.items():
                    field_mappings[excel_field] = {
                        'field_type': config.get('field_type'),
                        'data_type': config.get('data_type'),
                        'is_required': config.get('is_required', False)
                    }
                self.logger.info(f"🔧 [关键修复] 获取到 {len(field_mappings)} 个字段映射配置")

            # 调用现有的show_preview_data方法，传递字段映射配置
            self.show_preview_data(self.current_sheet_name, data_dicts, field_mappings)

            self.logger.info(f"成功更新预览数据: {len(data_dicts)} 行, {len(df.columns)} 列")

        except Exception as e:
            error_msg = f"更新预览数据失败: {e}"
            self.logger.error(error_msg)
            self.clear_preview()

    def refresh_preview_with_formatting(self):
        """刷新预览，重新应用字段类型格式化"""
        try:
            if not hasattr(self, 'current_preview_data') or not self.current_preview_data:
                self.logger.warning("没有缓存的预览数据可刷新")
                return

            # 获取父窗口的映射配置
            parent_window = self.parent()
            while parent_window and not hasattr(parent_window, 'mapping_tab'):
                parent_window = parent_window.parent()

            if parent_window and hasattr(parent_window.mapping_tab, 'get_current_mapping_config'):
                mapping_config = parent_window.mapping_tab.get_current_mapping_config()

                # 转换为预览需要的格式
                field_mappings = {}
                for excel_field, config in mapping_config.items():
                    field_mappings[excel_field] = {
                        'field_type': config.get('field_type'),
                        'data_type': config.get('data_type'),
                        'is_required': config.get('is_required', False)
                    }

                # 重新显示数据，应用格式化
                self.show_preview_data(
                    self.current_sheet_name,
                    self.current_preview_data,
                    field_mappings
                )

                self.logger.info(f"预览已刷新，字段类型格式化已应用 - {len(field_mappings)} 个字段映射")
            else:
                self.logger.warning("无法获取字段映射配置")

        except Exception as e:
            self.logger.error(f"刷新格式化预览失败: {e}")

    def clear_preview(self):
        """清空预览内容"""
        try:
            self.preview_table.setRowCount(0)
            self.preview_table.setColumnCount(0)
            self.record_count_label.setText("记录数: 0")
            self.current_preview_data = []  # 清空缓存数据

            # 重置验证状态
            self.validation_status_label.setText("验证状态: 未验证")
            self.validation_status_label.setStyleSheet("font-weight: bold; color: #666;")
            self.error_list.clear()

        except Exception as e:
            self.logger.error(f"清空预览内容失败: {e}")

    # 🔧 [方案B实施] 新增辅助方法
    
    def _enhance_configs_with_version_control(self, table_name: str, saved_configs: dict) -> dict:
        """🔧 [方案B实施] 增强配置加载：应用版本控制和冲突检测"""
        try:
            if not saved_configs:
                return {}
            
            enhanced_configs = {}
            conflict_resolved_count = 0
            user_modified_count = 0
            
            for excel_field, field_config in saved_configs.items():
                try:
                    # 🔧 [方案B] 获取字段的完整版本信息
                    versioned_config = self.config_sync_manager.get_field_config_with_version(table_name, excel_field)
                    
                    if versioned_config:
                        # 🔧 [方案B] 检查配置是否应该被应用
                        should_apply = self._should_apply_field_config(table_name, excel_field, versioned_config)
                        
                        if should_apply:
                            enhanced_configs[excel_field] = versioned_config
                            
                            # 统计信息
                            if versioned_config.get('config_source') == 'user_modified':
                                user_modified_count += 1
                            conflict_resolved_count += 1
                            
                            self.logger.debug(f"🔧 [方案B] 字段 '{excel_field}' 配置已增强，版本: {versioned_config.get('config_version', 'unknown')}")
                        else:
                            self.logger.debug(f"🔧 [方案B] 字段 '{excel_field}' 配置被跳过（优先级较低）")
                    else:
                        # 如果没有版本信息，使用原始配置
                        enhanced_configs[excel_field] = field_config
                        self.logger.debug(f"🔧 [方案B] 字段 '{excel_field}' 使用原始配置（无版本信息）")
                        
                except Exception as e:
                    self.logger.warning(f"🔧 [方案B] 增强字段 '{excel_field}' 配置失败: {e}")
                    # 降级：使用原始配置
                    enhanced_configs[excel_field] = field_config
            
            self.logger.info(f"🔧 [方案B] 配置增强完成: 冲突解决 {conflict_resolved_count} 个，用户修改 {user_modified_count} 个")
            return enhanced_configs
            
        except Exception as e:
            self.logger.error(f"🔧 [方案B] 配置增强失败: {e}")
            # 降级：返回原始配置
            return saved_configs
    
    def _should_apply_field_config(self, table_name: str, excel_field: str, field_config: Dict[str, Any]) -> bool:
        """🔧 [方案B实施] 判断是否应该应用字段配置"""
        try:
            # 检查配置来源
            config_source = field_config.get('config_source', 'unknown')
            
            # 用户修改的配置总是优先应用
            if config_source == 'user_modified':
                return True
            
            # 检查配置稳定性
            stability_score = field_config.get('stability_score', 0.0)
            if stability_score >= 80.0:  # 高稳定性配置
                return True
            
            # 检查用户修改次数
            user_mod_count = field_config.get('user_modification_count', 0)
            if user_mod_count > 0:  # 曾经被用户修改过
                return True
            
            # 检查时间因素
            current_time = time.time()
            last_modified = field_config.get('last_modified', current_time)
            time_diff = current_time - last_modified
            
            # 24小时内的配置优先应用
            if time_diff < 86400:
                return True
            
            # 智能推断的配置，检查是否与当前UI状态冲突
            current_ui_config = self._get_current_ui_field_config(excel_field)
            if current_ui_config:
                # 如果UI状态与保存的配置不同，可能需要冲突检测
                if (current_ui_config.get('field_type') != field_config.get('field_type') or
                    current_ui_config.get('data_type') != field_config.get('data_type')):
                    # 使用ConfigSyncManager的冲突解决机制
                    resolved_config = self.config_sync_manager.resolve_config_conflicts(
                        table_name, excel_field, current_ui_config, field_config
                    )
                    # 如果解决后的配置与保存的配置相同，则应用
                    return resolved_config == field_config
            
            return False
            
        except Exception as e:
            self.logger.debug(f"🔧 [方案B] 判断是否应用字段配置失败: {e}")
            return True  # 出错时默认应用
    
    def _get_current_ui_field_config(self, excel_field: str) -> Optional[Dict[str, Any]]:
        """🔧 [方案B实施] 获取当前UI中字段的配置状态"""
        try:
            if not hasattr(self, 'field_state_tracking'):
                return None
            
            # 查找字段在表格中的行
            for row in range(self.field_state_tracking.get('mapping_table', {}).rowCount() if hasattr(self, 'field_state_tracking') and 'mapping_table' in self.field_state_tracking else 0):
                excel_item = self.field_state_tracking.get('mapping_table', {}).item(row, 0) if hasattr(self, 'field_state_tracking') and 'mapping_table' in self.field_state_tracking else None
                if excel_item and excel_item.text() == excel_field:
                    # 获取字段类型
                    field_type_combo = self.field_state_tracking.get('mapping_table', {}).cellWidget(row, 3) if hasattr(self, 'field_state_tracking') and 'mapping_table' in self.field_state_tracking else None
                    field_type = field_type_combo.currentData() if field_type_combo else None
                    
                    # 获取数据类型
                    data_type_combo = self.field_state_tracking.get('mapping_table', {}).cellWidget(row, 4) if hasattr(self, 'field_state_tracking') and 'mapping_table' in self.field_state_tracking else None
                    data_type = data_type_combo.currentText() if data_type_combo else None
                    
                    # 获取是否必填
                    required_item = self.field_state_tracking.get('mapping_table', {}).item(row, 5) if hasattr(self, 'field_state_tracking') and 'mapping_table' in self.field_state_tracking else None
                    is_required = required_item.checkState() == Qt.Checked if required_item else False
                    
                    return {
                        'field_type': field_type,
                        'data_type': data_type,
                        'is_required': is_required,
                        'last_modified': time.time()
                    }
            
            return None
            
        except Exception as e:
            self.logger.debug(f"🔧 [方案B] 获取当前UI字段配置失败: {e}")
            return None
    
    def _update_field_state_tracking(self, excel_field: str, field_config: Dict[str, Any]):
        """🔧 [方案B实施] 更新字段状态跟踪"""
        try:
            if not hasattr(self, 'field_state_tracking'):
                self.field_state_tracking = {}
            
            # 更新字段状态
            if excel_field not in self.field_state_tracking:
                self.field_state_tracking[excel_field] = {}
            
            current_time = time.time()
            self.field_state_tracking[excel_field].update({
                'last_user_modification': current_time,
                'user_modification_count': self.field_state_tracking[excel_field].get('user_modification_count', 0) + 1,
                'current_config': field_config.copy(),
                'last_modified': current_time
            })
            
            self.logger.debug(f"🔧 [方案B] 字段状态跟踪已更新: {excel_field}")
            
        except Exception as e:
            self.logger.debug(f"🔧 [方案B] 更新字段状态跟踪失败: {e}")
    
    def _smart_save_user_modified_configs(self) -> bool:
        """🔧 [方案B实施] 智能保存：只保存用户明确修改的字段配置"""
        try:
            if not hasattr(self, 'field_state_tracking'):
                self.logger.debug("🔧 [方案B] 字段状态跟踪未初始化")
                return False
            
            if not hasattr(self, 'mapping_table'):
                self.logger.debug("🔧 [方案B] 映射表格未初始化")
                return False
            
            saved_count = 0
            total_modified = 0
            
            # 遍历所有字段，检查是否有用户修改
            for row in range(self.mapping_table.rowCount()):
                try:
                    excel_item = self.mapping_table.item(row, 0)
                    if not excel_item:
                        continue
                    
                    excel_field = excel_item.text()
                    
                    # 检查字段是否被用户修改过
                    if excel_field in self.field_state_tracking:
                        field_state = self.field_state_tracking[excel_field]
                        last_user_mod = field_state.get('last_user_modification', 0)
                        current_time = time.time()
                        
                        # 如果字段在最近5分钟内被用户修改过，则保存
                        if (current_time - last_user_mod) < 300:  # 5分钟
                            total_modified += 1
                            
                            # 获取当前UI状态
                            field_type_combo = self.mapping_table.cellWidget(row, 3)
                            field_type = field_type_combo.currentData() if field_type_combo else None
                            
                            data_type_combo = self.mapping_table.cellWidget(row, 4)
                            data_type = data_type_combo.currentText() if data_type_combo else None
                            
                            required_item = self.mapping_table.item(row, 5)
                            is_required = required_item.checkState() == Qt.Checked if required_item else False
                            
                            # 构建字段配置
                            field_config = {
                                'target_field': excel_field,
                                'field_type': field_type,
                                'data_type': data_type,
                                'is_required': is_required,
                                'last_modified': current_time,
                                'user_modified': True,
                                'config_source': 'user_modified',
                                'modification_timestamp': current_time
                            }
                            
                            # 保存到ConfigSyncManager
                            if hasattr(self, 'config_sync_manager') and self.config_sync_manager:
                                table_name = self._generate_table_name()
                                success = self.config_sync_manager.save_field_mapping(
                                    table_name, excel_field, field_config
                                )
                                
                                if success:
                                    saved_count += 1
                                    self.logger.debug(f"🔧 [方案B] 字段 '{excel_field}' 用户修改配置已保存")
                                else:
                                    self.logger.warning(f"🔧 [方案B] 字段 '{excel_field}' 用户修改配置保存失败")
                            
                except Exception as e:
                    self.logger.warning(f"🔧 [方案B] 处理第{row}行字段保存失败: {e}")
                    continue
            
            if total_modified > 0:
                self.logger.info(f"🔧 [方案B] 智能保存完成: {saved_count}/{total_modified} 个用户修改字段已保存")
                return saved_count > 0
            else:
                self.logger.info("🔧 [方案B] 没有检测到用户修改的字段，跳过智能保存")
                return True  # 没有修改也算成功
                
        except Exception as e:
            self.logger.error(f"🔧 [方案B] 智能保存失败: {e}")
            return False


# 测试用主函数
if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)

    # 设置应用样式
    app.setStyle('Fusion')

    window = UnifiedDataImportWindow()
    window.show()

    sys.exit(app.exec_())
