{"timestamp": "2025-08-28T23:42:00.099905", "table_name": "change_data_2025_12_retired_employees", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-08-28T23:42:00.362498", "table_name": "change_data_2025_12_pension_employees", "action": "save", "field_count": 27, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "补发", "借支", "应发工资", "公积", "保险扣款", "备注"]}
{"timestamp": "2025-08-28T23:42:00.671453", "table_name": "change_data_2025_12_active_employees", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-08-28T23:42:00.977748", "table_name": "change_data_2025_12_a_grade_employees", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-01T16:28:09.245070", "table_name": "mapping_config_20250901_162809", "action": "save", "field_count": 1, "fields": ["test_field"]}
{"timestamp": "2025-09-02T08:48:53.648150", "table_name": "mapping_config_20250902_084853", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T08:48:55.835643", "table_name": "mapping_config_20250902_084855", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T08:48:58.241813", "table_name": "mapping_config_20250902_084858", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T08:49:00.632322", "table_name": "mapping_config_20250902_084900", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T08:49:02.069862", "table_name": "mapping_config_20250902_084902", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T08:49:03.507429", "table_name": "mapping_config_20250902_084903", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T08:49:06.226306", "table_name": "mapping_config_20250902_084906", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T08:49:07.163542", "table_name": "mapping_config_20250902_084907", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T08:49:07.960655", "table_name": "mapping_config_20250902_084907", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T08:49:08.569795", "table_name": "mapping_config_20250902_084908", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T10:57:09.378754", "table_name": "mapping_config_20250902_105709", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T10:57:11.488090", "table_name": "mapping_config_20250902_105711", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T10:57:12.409653", "table_name": "mapping_config_20250902_105712", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T10:57:13.003274", "table_name": "mapping_config_20250902_105713", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T10:57:40.493053", "table_name": "mapping_config_20250902_105740", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T10:57:42.586803", "table_name": "mapping_config_20250902_105742", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T10:57:44.461637", "table_name": "mapping_config_20250902_105744", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T10:57:45.555205", "table_name": "mapping_config_20250902_105745", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T10:57:46.321409", "table_name": "mapping_config_20250902_105746", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T10:57:47.492759", "table_name": "mapping_config_20250902_105747", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T10:57:48.602368", "table_name": "mapping_config_20250902_105748", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T11:36:52.478679", "table_name": "mapping_config_20250902_113652", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T11:36:56.007438", "table_name": "mapping_config_20250902_113656", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T11:36:58.711429", "table_name": "mapping_config_20250902_113658", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T11:37:04.678680", "table_name": "mapping_config_20250902_113704", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T11:37:07.313345", "table_name": "mapping_config_20250902_113707", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T11:37:08.228805", "table_name": "mapping_config_20250902_113708", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T11:37:18.019741", "table_name": "mapping_config_20250902_113718", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T11:37:19.311274", "table_name": "mapping_config_20250902_113719", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T11:37:30.473660", "table_name": "mapping_config_20250902_113730", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T11:37:32.792669", "table_name": "mapping_config_20250902_113732", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T11:37:34.278217", "table_name": "mapping_config_20250902_113734", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T11:37:34.845458", "table_name": "mapping_config_20250902_113734", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T11:37:40.381818", "table_name": "mapping_config_20250902_113740", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T11:37:41.157037", "table_name": "mapping_config_20250902_113741", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:07:18.551496", "table_name": "mapping_config_20250902_130718", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:07:20.918578", "table_name": "mapping_config_20250902_130720", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:07:24.472424", "table_name": "mapping_config_20250902_130724", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:07:27.843960", "table_name": "mapping_config_20250902_130727", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:07:41.230869", "table_name": "mapping_config_20250902_130741", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:07:41.842162", "table_name": "mapping_config_20250902_130741", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:07:50.679899", "table_name": "mapping_config_20250902_130750", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:07:52.096472", "table_name": "mapping_config_20250902_130752", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:07:57.449293", "table_name": "mapping_config_20250902_130757", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:08:00.009959", "table_name": "mapping_config_20250902_130759", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:08:03.343831", "table_name": "mapping_config_20250902_130803", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:08:05.846873", "table_name": "mapping_config_20250902_130805", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:08:11.554288", "table_name": "mapping_config_20250902_130811", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:08:12.232011", "table_name": "mapping_config_20250902_130812", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:08:17.403684", "table_name": "mapping_config_20250902_130817", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:08:19.665795", "table_name": "mapping_config_20250902_130819", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:08:52.560416", "table_name": "mapping_config_20250902_130852", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:08:53.747095", "table_name": "mapping_config_20250902_130853", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:09:06.365342", "table_name": "mapping_config_20250902_130906", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:11:05.404129", "table_name": "mapping_config_20250902_131105", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:11:11.506879", "table_name": "mapping_config_20250902_131111", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:11:16.493501", "table_name": "mapping_config_20250902_131116", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:11:18.783692", "table_name": "mapping_config_20250902_131118", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:11:20.911014", "table_name": "mapping_config_20250902_131120", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:11:23.692472", "table_name": "mapping_config_20250902_131123", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:11:26.217906", "table_name": "mapping_config_20250902_131126", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:11:32.268029", "table_name": "mapping_config_20250902_131132", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:11:35.882993", "table_name": "mapping_config_20250902_131135", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:11:54.537777", "table_name": "mapping_config_20250902_131154", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:12:03.353380", "table_name": "mapping_config_20250902_131203", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:12:06.569575", "table_name": "mapping_config_20250902_131206", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:12:36.424633", "table_name": "mapping_config_20250902_131236", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:12:42.802604", "table_name": "mapping_config_20250902_131242", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:12:50.979662", "table_name": "mapping_config_20250902_131250", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:12:53.632710", "table_name": "mapping_config_20250902_131253", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:12:55.232687", "table_name": "mapping_config_20250902_131255", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:12:57.915093", "table_name": "mapping_config_20250902_131257", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:13:01.282658", "table_name": "mapping_config_20250902_131301", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:13:03.491533", "table_name": "mapping_config_20250902_131303", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:13:05.299418", "table_name": "mapping_config_20250902_131305", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:24:23.166764", "table_name": "mapping_config_20250902_132423", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:24:26.009357", "table_name": "mapping_config_20250902_132425", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:24:28.135715", "table_name": "mapping_config_20250902_132428", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:24:30.834782", "table_name": "mapping_config_20250902_132430", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:24:34.294161", "table_name": "mapping_config_20250902_132434", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:24:46.681076", "table_name": "mapping_config_20250902_132446", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:24:50.314757", "table_name": "mapping_config_20250902_132450", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:24:56.401679", "table_name": "mapping_config_20250902_132456", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:25:00.926436", "table_name": "mapping_config_20250902_132500", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:25:03.711433", "table_name": "mapping_config_20250902_132503", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:25:05.043636", "table_name": "mapping_config_20250902_132505", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:25:10.060250", "table_name": "mapping_config_20250902_132510", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:25:11.272903", "table_name": "mapping_config_20250902_132511", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:25:12.619440", "table_name": "mapping_config_20250902_132512", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:25:31.932779", "table_name": "mapping_config_20250902_132531", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:25:40.675198", "table_name": "mapping_config_20250902_132540", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:25:43.329385", "table_name": "mapping_config_20250902_132543", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:25:47.401398", "table_name": "mapping_config_20250902_132547", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:25:49.658359", "table_name": "mapping_config_20250902_132549", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:25:52.615637", "table_name": "mapping_config_20250902_132552", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:25:54.706351", "table_name": "mapping_config_20250902_132554", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:25:57.336762", "table_name": "mapping_config_20250902_132557", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:28:49.228324", "table_name": "mapping_config_20250902_132849", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:28:55.973702", "table_name": "mapping_config_20250902_132855", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:28:57.099039", "table_name": "mapping_config_20250902_132857", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:29:01.587483", "table_name": "mapping_config_20250902_132901", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:29:06.698896", "table_name": "mapping_config_20250902_132906", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:29:08.027916", "table_name": "mapping_config_20250902_132908", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:29:09.078210", "table_name": "mapping_config_20250902_132909", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:29:20.715456", "table_name": "mapping_config_20250902_132920", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:29:22.588014", "table_name": "mapping_config_20250902_132922", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:29:24.379125", "table_name": "mapping_config_20250902_132924", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:29:26.876517", "table_name": "mapping_config_20250902_132926", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:29:29.978075", "table_name": "mapping_config_20250902_132929", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:29:37.429207", "table_name": "mapping_config_20250902_132937", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:29:41.673876", "table_name": "mapping_config_20250902_132941", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:29:44.251412", "table_name": "mapping_config_20250902_132944", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:29:45.571503", "table_name": "mapping_config_20250902_132945", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:30:04.850940", "table_name": "mapping_config_20250902_133004", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T13:30:10.461764", "table_name": "mapping_config_20250902_133010", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T13:30:14.461422", "table_name": "mapping_config_20250902_133014", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T13:30:16.646891", "table_name": "mapping_config_20250902_133016", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T13:30:19.618997", "table_name": "mapping_config_20250902_133019", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T13:30:22.227852", "table_name": "mapping_config_20250902_133022", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T13:30:24.260166", "table_name": "mapping_config_20250902_133024", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T13:30:26.101217", "table_name": "mapping_config_20250902_133026", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T13:30:28.404823", "table_name": "mapping_config_20250902_133028", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T13:30:44.069818", "table_name": "mapping_config_20250902_133044", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T13:30:52.416349", "table_name": "mapping_config_20250902_133052", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T13:31:06.553041", "table_name": "mapping_config_20250902_133106", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T13:31:12.037149", "table_name": "mapping_config_20250902_133112", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T13:31:13.312576", "table_name": "mapping_config_20250902_133113", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T13:33:37.576842", "table_name": "mapping_config_20250902_133337", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T13:33:47.986336", "table_name": "mapping_config_20250902_133347", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T13:33:54.895606", "table_name": "mapping_config_20250902_133354", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T13:33:57.246555", "table_name": "mapping_config_20250902_133357", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T13:33:59.976743", "table_name": "mapping_config_20250902_133359", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T13:34:04.751804", "table_name": "mapping_config_20250902_133404", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:18:14.018991", "table_name": "mapping_config_20250902_151814", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:18:25.737346", "table_name": "mapping_config_20250902_151825", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:18:35.003704", "table_name": "mapping_config_20250902_151834", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:18:36.051427", "table_name": "mapping_config_20250902_151836", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:18:45.707769", "table_name": "mapping_config_20250902_151845", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:18:46.739204", "table_name": "mapping_config_20250902_151846", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:18:47.942404", "table_name": "mapping_config_20250902_151847", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:18:48.989064", "table_name": "mapping_config_20250902_151848", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:18:51.364640", "table_name": "mapping_config_20250902_151851", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:18:56.208611", "table_name": "mapping_config_20250902_151856", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:18:58.614097", "table_name": "mapping_config_20250902_151858", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:19:03.379287", "table_name": "mapping_config_20250902_151903", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:19:05.097850", "table_name": "mapping_config_20250902_151905", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:27:46.949511", "table_name": "mapping_config_20250902_152746", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:27:56.073144", "table_name": "mapping_config_20250902_152756", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:27:57.025856", "table_name": "mapping_config_20250902_152757", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:28:01.755262", "table_name": "mapping_config_20250902_152801", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:28:04.036567", "table_name": "mapping_config_20250902_152804", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:28:06.927135", "table_name": "mapping_config_20250902_152806", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:28:10.786489", "table_name": "mapping_config_20250902_152810", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:28:16.895643", "table_name": "mapping_config_20250902_152816", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:28:17.645326", "table_name": "mapping_config_20250902_152817", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:28:25.786032", "table_name": "mapping_config_20250902_152825", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:28:26.973476", "table_name": "mapping_config_20250902_152826", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:28:31.473671", "table_name": "mapping_config_20250902_152831", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:28:35.645426", "table_name": "mapping_config_20250902_152835", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:28:39.860619", "table_name": "mapping_config_20250902_152839", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:28:40.637059", "table_name": "mapping_config_20250902_152840", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:29:00.472731", "table_name": "mapping_config_20250902_152900", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:29:01.035390", "table_name": "mapping_config_20250902_152901", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:29:03.128891", "table_name": "mapping_config_20250902_152903", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:29:07.660924", "table_name": "mapping_config_20250902_152907", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:29:08.848056", "table_name": "mapping_config_20250902_152908", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:29:09.488591", "table_name": "mapping_config_20250902_152909", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:29:13.128893", "table_name": "mapping_config_20250902_152913", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:29:13.957213", "table_name": "mapping_config_20250902_152913", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:29:15.332180", "table_name": "mapping_config_20250902_152915", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T15:29:18.754175", "table_name": "mapping_config_20250902_152918", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:29:21.816356", "table_name": "mapping_config_20250902_152921", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:29:24.722561", "table_name": "mapping_config_20250902_152924", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:29:29.360899", "table_name": "mapping_config_20250902_152929", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:29:41.721716", "table_name": "mapping_config_20250902_152941", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:29:47.069038", "table_name": "mapping_config_20250902_152947", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:29:47.830975", "table_name": "mapping_config_20250902_152947", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:30:05.455454", "table_name": "mapping_config_20250902_153005", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:30:08.345552", "table_name": "mapping_config_20250902_153008", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:30:13.971008", "table_name": "mapping_config_20250902_153013", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:31:52.435530", "table_name": "mapping_config_20250902_153152", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:32:26.698336", "table_name": "mapping_config_20250902_153226", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:32:31.432555", "table_name": "mapping_config_20250902_153231", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:33:03.245351", "table_name": "mapping_config_20250902_153303", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:33:26.698551", "table_name": "mapping_config_20250902_153326", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:33:28.697734", "table_name": "mapping_config_20250902_153328", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:33:31.306708", "table_name": "mapping_config_20250902_153331", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:33:44.098349", "table_name": "mapping_config_20250902_153344", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T15:33:45.395359", "table_name": "mapping_config_20250902_153345", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T16:21:21.763522", "table_name": "mapping_config_20250902_162121", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:21:26.705781", "table_name": "mapping_config_20250902_162126", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:21:29.637965", "table_name": "mapping_config_20250902_162129", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:21:31.637994", "table_name": "mapping_config_20250902_162131", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:21:33.758069", "table_name": "mapping_config_20250902_162133", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:21:36.085928", "table_name": "mapping_config_20250902_162136", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:21:38.442995", "table_name": "mapping_config_20250902_162138", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:21:44.026834", "table_name": "mapping_config_20250902_162144", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:21:48.733594", "table_name": "mapping_config_20250902_162148", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:21:51.383138", "table_name": "mapping_config_20250902_162151", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:21:52.636768", "table_name": "mapping_config_20250902_162152", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:22:31.322327", "table_name": "mapping_config_20250902_162231", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:22:33.738382", "table_name": "mapping_config_20250902_162233", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:22:40.587236", "table_name": "mapping_config_20250902_162240", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:22:41.778632", "table_name": "mapping_config_20250902_162241", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:22:51.519535", "table_name": "mapping_config_20250902_162251", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T16:22:59.734694", "table_name": "mapping_config_20250902_162259", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T16:23:03.966974", "table_name": "mapping_config_20250902_162303", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T16:23:08.495508", "table_name": "mapping_config_20250902_162308", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T16:23:38.883067", "table_name": "mapping_config_20250902_162338", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T16:23:39.538326", "table_name": "mapping_config_20250902_162339", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T16:23:45.874727", "table_name": "mapping_config_20250902_162345", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T16:23:46.867979", "table_name": "mapping_config_20250902_162346", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T16:23:48.297301", "table_name": "mapping_config_20250902_162348", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T16:23:50.283714", "table_name": "mapping_config_20250902_162350", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T16:23:57.115997", "table_name": "mapping_config_20250902_162357", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T16:23:58.292381", "table_name": "mapping_config_20250902_162358", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T16:24:03.852154", "table_name": "mapping_config_20250902_162403", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:24:04.523568", "table_name": "mapping_config_20250902_162404", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:24:10.664560", "table_name": "mapping_config_20250902_162410", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:24:11.669020", "table_name": "mapping_config_20250902_162411", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:24:14.455703", "table_name": "mapping_config_20250902_162414", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:24:16.272087", "table_name": "mapping_config_20250902_162416", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:24:19.321793", "table_name": "mapping_config_20250902_162419", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:24:22.111951", "table_name": "mapping_config_20250902_162422", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:24:25.608356", "table_name": "mapping_config_20250902_162425", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:24:28.192066", "table_name": "mapping_config_20250902_162428", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:24:29.924775", "table_name": "mapping_config_20250902_162429", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:24:46.365146", "table_name": "mapping_config_20250902_162446", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:24:47.795116", "table_name": "mapping_config_20250902_162447", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:24:49.514813", "table_name": "mapping_config_20250902_162449", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:24:51.667440", "table_name": "mapping_config_20250902_162451", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:25:04.077470", "table_name": "mapping_config_20250902_162504", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:25:07.971688", "table_name": "mapping_config_20250902_162507", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:25:12.681804", "table_name": "mapping_config_20250902_162512", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:25:14.964825", "table_name": "mapping_config_20250902_162514", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:25:17.640230", "table_name": "mapping_config_20250902_162517", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:25:21.001119", "table_name": "mapping_config_20250902_162520", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:25:25.986560", "table_name": "mapping_config_20250902_162525", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:25:33.163384", "table_name": "mapping_config_20250902_162533", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:25:35.705149", "table_name": "mapping_config_20250902_162535", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:25:37.173251", "table_name": "mapping_config_20250902_162537", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:26:38.096142", "table_name": "mapping_config_20250902_162638", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:26:40.981222", "table_name": "mapping_config_20250902_162640", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:38:30.467373", "table_name": "mapping_config_20250902_163830", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:38:34.350816", "table_name": "mapping_config_20250902_163834", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:38:37.138705", "table_name": "mapping_config_20250902_163837", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:38:40.328418", "table_name": "mapping_config_20250902_163840", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:38:43.102741", "table_name": "mapping_config_20250902_163843", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:38:43.686630", "table_name": "mapping_config_20250902_163843", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:38:51.067063", "table_name": "mapping_config_20250902_163851", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:38:54.540064", "table_name": "mapping_config_20250902_163854", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:38:57.132437", "table_name": "mapping_config_20250902_163857", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:38:58.707798", "table_name": "mapping_config_20250902_163858", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:38:59.677177", "table_name": "mapping_config_20250902_163859", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T16:39:08.188301", "table_name": "mapping_config_20250902_163908", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:10:12.777536", "table_name": "mapping_config_20250902_171012", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:10:29.631440", "table_name": "mapping_config_20250902_171029", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:10:30.803409", "table_name": "mapping_config_20250902_171030", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:10:38.073346", "table_name": "mapping_config_20250902_171038", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:10:41.390982", "table_name": "mapping_config_20250902_171041", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:10:43.649197", "table_name": "mapping_config_20250902_171043", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:10:45.816959", "table_name": "mapping_config_20250902_171045", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:10:47.621255", "table_name": "mapping_config_20250902_171047", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:10:53.993928", "table_name": "mapping_config_20250902_171053", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:11:05.840411", "table_name": "mapping_config_20250902_171105", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:11:07.219198", "table_name": "mapping_config_20250902_171107", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:11:15.786556", "table_name": "mapping_config_20250902_171115", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:11:19.489420", "table_name": "mapping_config_20250902_171119", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:11:21.981833", "table_name": "mapping_config_20250902_171121", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:11:33.432229", "table_name": "mapping_config_20250902_171133", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:11:47.229750", "table_name": "mapping_config_20250902_171147", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:19:33.805614", "table_name": "mapping_config_20250902_171933", "action": "save", "field_count": 4, "fields": ["工号", "人员类别代码", "姓名", "基本工资"]}
{"timestamp": "2025-09-02T17:19:33.847710", "table_name": "mapping_config_20250902_171933", "action": "save", "field_count": 4, "fields": ["工号", "人员类别代码", "姓名", "基本工资"]}
{"timestamp": "2025-09-02T17:19:33.899316", "table_name": "mapping_config_20250902_171933", "action": "save", "field_count": 4, "fields": ["工号", "人员类别代码", "姓名", "基本工资"]}
{"timestamp": "2025-09-02T17:19:37.304725", "table_name": "mapping_config_20250902_171937", "action": "save", "field_count": 4, "fields": ["工号", "人员类别代码", "姓名", "基本工资"]}
{"timestamp": "2025-09-02T17:20:57.581482", "table_name": "mapping_config_20250902_172057", "action": "save", "field_count": 4, "fields": ["工号", "人员类别代码", "姓名", "基本工资"]}
{"timestamp": "2025-09-02T17:20:57.831614", "table_name": "mapping_config_20250902_172057", "action": "save", "field_count": 4, "fields": ["工号", "人员类别代码", "姓名", "基本工资"]}
{"timestamp": "2025-09-02T17:20:57.886034", "table_name": "mapping_config_20250902_172057", "action": "save", "field_count": 4, "fields": ["工号", "人员类别代码", "姓名", "基本工资"]}
{"timestamp": "2025-09-02T17:21:00.566138", "table_name": "mapping_config_20250902_172100", "action": "save", "field_count": 4, "fields": ["工号", "人员类别代码", "姓名", "基本工资"]}
{"timestamp": "2025-09-02T17:23:51.473626", "table_name": "mapping_config_20250902_172351", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:23:57.003970", "table_name": "mapping_config_20250902_172356", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:24:00.018186", "table_name": "mapping_config_20250902_172359", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:24:24.956566", "table_name": "mapping_config_20250902_172424", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:24:37.750196", "table_name": "mapping_config_20250902_172437", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:24:38.474902", "table_name": "mapping_config_20250902_172438", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:24:39.443531", "table_name": "mapping_config_20250902_172439", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:24:44.157979", "table_name": "mapping_config_20250902_172444", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:24:48.455434", "table_name": "mapping_config_20250902_172448", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:24:50.628169", "table_name": "mapping_config_20250902_172450", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:24:52.940018", "table_name": "mapping_config_20250902_172452", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:24:56.050258", "table_name": "mapping_config_20250902_172456", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:24:58.157661", "table_name": "mapping_config_20250902_172458", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:25:04.954920", "table_name": "mapping_config_20250902_172504", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:25:06.767466", "table_name": "mapping_config_20250902_172506", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:25:11.079526", "table_name": "mapping_config_20250902_172511", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:25:14.422651", "table_name": "mapping_config_20250902_172514", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:25:16.985341", "table_name": "mapping_config_20250902_172516", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:25:23.187223", "table_name": "mapping_config_20250902_172523", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:25:24.885932", "table_name": "mapping_config_20250902_172524", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:25:24.954422", "table_name": "mapping_config_20250902_172524", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:25:29.874016", "table_name": "mapping_config_20250902_172529", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:25:32.967628", "table_name": "mapping_config_20250902_172532", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:25:33.594244", "table_name": "mapping_config_20250902_172533", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:25:34.344276", "table_name": "mapping_config_20250902_172534", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:39:58.384885", "table_name": "mapping_config_20250902_173958", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:04.011243", "table_name": "mapping_config_20250902_174003", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:05.193674", "table_name": "mapping_config_20250902_174005", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:08.610525", "table_name": "mapping_config_20250902_174008", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:10.507252", "table_name": "mapping_config_20250902_174010", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:13.106060", "table_name": "mapping_config_20250902_174013", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:16.762958", "table_name": "mapping_config_20250902_174016", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:19.268121", "table_name": "mapping_config_20250902_174019", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:25.298103", "table_name": "mapping_config_20250902_174025", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:30.080762", "table_name": "mapping_config_20250902_174030", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:30.705346", "table_name": "mapping_config_20250902_174030", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:31.815416", "table_name": "mapping_config_20250902_174031", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:32.953762", "table_name": "mapping_config_20250902_174032", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:35.209438", "table_name": "mapping_config_20250902_174035", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:39.786887", "table_name": "mapping_config_20250902_174039", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:42.427091", "table_name": "mapping_config_20250902_174042", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:43.999251", "table_name": "mapping_config_20250902_174043", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:47.593643", "table_name": "mapping_config_20250902_174047", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:49.780801", "table_name": "mapping_config_20250902_174049", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:49.847868", "table_name": "mapping_config_20250902_174049", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:54.478862", "table_name": "mapping_config_20250902_174054", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:40:56.711196", "table_name": "mapping_config_20250902_174056", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:41:00.912005", "table_name": "mapping_config_20250902_174100", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:41:01.791049", "table_name": "mapping_config_20250902_174101", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:41:04.059704", "table_name": "mapping_config_20250902_174104", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:41:05.630550", "table_name": "mapping_config_20250902_174105", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:41:07.682851", "table_name": "mapping_config_20250902_174107", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:41:17.265532", "table_name": "mapping_config_20250902_174117", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:41:19.035072", "table_name": "mapping_config_20250902_174119", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:41:22.313279", "table_name": "mapping_config_20250902_174122", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:41:51.484122", "table_name": "mapping_config_20250902_174151", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T17:41:54.959721", "table_name": "mapping_config_20250902_174154", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T17:41:58.753913", "table_name": "mapping_config_20250902_174158", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T17:42:17.987249", "table_name": "mapping_config_20250902_174217", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T17:42:32.184649", "table_name": "mapping_config_20250902_174232", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:42:36.980020", "table_name": "mapping_config_20250902_174236", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:42:39.354924", "table_name": "mapping_config_20250902_174239", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:42:41.620297", "table_name": "mapping_config_20250902_174241", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:42:47.635927", "table_name": "mapping_config_20250902_174247", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:42:49.116891", "table_name": "mapping_config_20250902_174249", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:42:55.050887", "table_name": "mapping_config_20250902_174255", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:42:55.699134", "table_name": "mapping_config_20250902_174255", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:43:00.156715", "table_name": "mapping_config_20250902_174300", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:43:02.052849", "table_name": "mapping_config_20250902_174302", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T17:43:03.401191", "table_name": "mapping_config_20250902_174303", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:05:01.590616", "table_name": "mapping_config_20250902_180501", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:05:05.645971", "table_name": "mapping_config_20250902_180505", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:05:10.124839", "table_name": "mapping_config_20250902_180510", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:05:12.183424", "table_name": "mapping_config_20250902_180512", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:05:14.406321", "table_name": "mapping_config_20250902_180514", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:05:17.118915", "table_name": "mapping_config_20250902_180517", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:05:20.094561", "table_name": "mapping_config_20250902_180520", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:05:22.319141", "table_name": "mapping_config_20250902_180522", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:05:23.788375", "table_name": "mapping_config_20250902_180523", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:05:33.392468", "table_name": "mapping_config_20250902_180533", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:05:35.275640", "table_name": "mapping_config_20250902_180535", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:05:35.338127", "table_name": "mapping_config_20250902_180535", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:05:39.879186", "table_name": "mapping_config_20250902_180539", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:08:28.666169", "table_name": "mapping_config_20250902_180828", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:08:32.357279", "table_name": "mapping_config_20250902_180832", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:08:36.254001", "table_name": "mapping_config_20250902_180836", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:08:38.373223", "table_name": "mapping_config_20250902_180838", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:08:40.597302", "table_name": "mapping_config_20250902_180840", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:08:43.629810", "table_name": "mapping_config_20250902_180843", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:08:45.991105", "table_name": "mapping_config_20250902_180845", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:08:48.498987", "table_name": "mapping_config_20250902_180848", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:08:50.401166", "table_name": "mapping_config_20250902_180850", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:08:51.776291", "table_name": "mapping_config_20250902_180851", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:08:59.108054", "table_name": "mapping_config_20250902_180859", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:08:59.835291", "table_name": "mapping_config_20250902_180859", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:09:00.348544", "table_name": "mapping_config_20250902_180900", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:09:04.066613", "table_name": "mapping_config_20250902_180904", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:09:04.144461", "table_name": "mapping_config_20250902_180904", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:09:09.036860", "table_name": "mapping_config_20250902_180909", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:09:09.898892", "table_name": "mapping_config_20250902_180909", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:09:14.174460", "table_name": "mapping_config_20250902_180914", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:09:17.032147", "table_name": "mapping_config_20250902_180916", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:09:18.639839", "table_name": "mapping_config_20250902_180918", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:09:20.023478", "table_name": "mapping_config_20250902_180919", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:09:27.875570", "table_name": "mapping_config_20250902_180927", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:09:28.416127", "table_name": "mapping_config_20250902_180928", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T18:09:28.958094", "table_name": "mapping_config_20250902_180928", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:01:03.205230", "table_name": "mapping_config_20250902_190103", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:01:10.848388", "table_name": "mapping_config_20250902_190110", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:01:14.260791", "table_name": "mapping_config_20250902_190114", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:01:18.284141", "table_name": "mapping_config_20250902_190118", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:01:20.927272", "table_name": "mapping_config_20250902_190120", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:01:23.344456", "table_name": "mapping_config_20250902_190123", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:01:25.516233", "table_name": "mapping_config_20250902_190125", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:01:34.978986", "table_name": "mapping_config_20250902_190134", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:01:36.378683", "table_name": "mapping_config_20250902_190136", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:01:43.138383", "table_name": "mapping_config_20250902_190143", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:01:46.109733", "table_name": "mapping_config_20250902_190146", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:01:47.658353", "table_name": "mapping_config_20250902_190147", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:02:20.750799", "table_name": "mapping_config_20250902_190220", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:02:24.470799", "table_name": "mapping_config_20250902_190224", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:20:53.041720", "table_name": "mapping_config_20250902_192052", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:20:54.765446", "table_name": "mapping_config_20250902_192054", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:20:56.473931", "table_name": "mapping_config_20250902_192056", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:20:59.757319", "table_name": "mapping_config_20250902_192059", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:21:02.522650", "table_name": "mapping_config_20250902_192102", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:21:07.075341", "table_name": "mapping_config_20250902_192107", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:21:11.628225", "table_name": "mapping_config_20250902_192111", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:21:13.918669", "table_name": "mapping_config_20250902_192113", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:21:15.664048", "table_name": "mapping_config_20250902_192115", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:22:10.421377", "table_name": "mapping_config_20250902_192210", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:22:12.802790", "table_name": "mapping_config_20250902_192212", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:22:15.166211", "table_name": "mapping_config_20250902_192215", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:22:25.473932", "table_name": "mapping_config_20250902_192225", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:22:27.840157", "table_name": "mapping_config_20250902_192227", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:25:07.812717", "table_name": "mapping_config_20250902_192507", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:25:10.651152", "table_name": "mapping_config_20250902_192510", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:25:13.482465", "table_name": "mapping_config_20250902_192513", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:25:17.303884", "table_name": "mapping_config_20250902_192517", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:25:18.942343", "table_name": "mapping_config_20250902_192518", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:25:34.749059", "table_name": "mapping_config_20250902_192534", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:25:38.459632", "table_name": "mapping_config_20250902_192538", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:25:41.828599", "table_name": "mapping_config_20250902_192541", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:25:44.220927", "table_name": "mapping_config_20250902_192544", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T19:34:34.639064", "table_name": "mapping_config_20250902_193434", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:37:04.606681", "table_name": "mapping_config_20250902_213704", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:37:12.257566", "table_name": "mapping_config_20250902_213712", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:37:15.833149", "table_name": "mapping_config_20250902_213715", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:37:17.903044", "table_name": "mapping_config_20250902_213717", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:37:22.313959", "table_name": "mapping_config_20250902_213722", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:37:24.724288", "table_name": "mapping_config_20250902_213724", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:37:30.328594", "table_name": "mapping_config_20250902_213730", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:37:33.807183", "table_name": "mapping_config_20250902_213733", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:37:37.183516", "table_name": "mapping_config_20250902_213737", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:37:44.673225", "table_name": "mapping_config_20250902_213744", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:37:46.299959", "table_name": "mapping_config_20250902_213746", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:37:53.734655", "table_name": "mapping_config_20250902_213753", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:38:01.880238", "table_name": "mapping_config_20250902_213801", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:38:05.313818", "table_name": "mapping_config_20250902_213805", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:38:35.472116", "table_name": "mapping_config_20250902_213835", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:38:38.848091", "table_name": "mapping_config_20250902_213838", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:38:40.109765", "table_name": "mapping_config_20250902_213840", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:38:41.119952", "table_name": "mapping_config_20250902_213841", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:38:51.615937", "table_name": "mapping_config_20250902_213851", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:59:18.012150", "table_name": "mapping_config_20250902_215917", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:59:23.364818", "table_name": "mapping_config_20250902_215923", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:59:30.476204", "table_name": "mapping_config_20250902_215930", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:59:34.221937", "table_name": "mapping_config_20250902_215934", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:59:36.433812", "table_name": "mapping_config_20250902_215936", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:59:39.388550", "table_name": "mapping_config_20250902_215939", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:59:44.117641", "table_name": "mapping_config_20250902_215944", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:59:45.938349", "table_name": "mapping_config_20250902_215945", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T21:59:56.859794", "table_name": "mapping_config_20250902_215956", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:00:02.260984", "table_name": "mapping_config_20250902_220002", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:00:03.954797", "table_name": "mapping_config_20250902_220003", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:00:11.048473", "table_name": "mapping_config_20250902_220011", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:00:11.864077", "table_name": "mapping_config_20250902_220011", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:00:16.045541", "table_name": "mapping_config_20250902_220015", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:00:18.839539", "table_name": "mapping_config_20250902_220018", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:00:23.127389", "table_name": "mapping_config_20250902_220023", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:00:23.220911", "table_name": "mapping_config_20250902_220023", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:00:27.299423", "table_name": "mapping_config_20250902_220027", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:00:27.989551", "table_name": "mapping_config_20250902_220027", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:00:34.521445", "table_name": "mapping_config_20250902_220034", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:29:24.964882", "table_name": "mapping_config_20250902_222924", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:29:47.937648", "table_name": "mapping_config_20250902_222947", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:29:52.918145", "table_name": "mapping_config_20250902_222952", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:30:00.461558", "table_name": "mapping_config_20250902_223000", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:30:13.462377", "table_name": "mapping_config_20250902_223013", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:30:15.878932", "table_name": "mapping_config_20250902_223015", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:30:34.147056", "table_name": "mapping_config_20250902_223034", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:30:40.008844", "table_name": "mapping_config_20250902_223039", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:30:42.647105", "table_name": "mapping_config_20250902_223042", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:30:44.626020", "table_name": "mapping_config_20250902_223044", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:30:59.947927", "table_name": "mapping_config_20250902_223059", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:31:11.361306", "table_name": "mapping_config_20250902_223111", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T22:31:25.696792", "table_name": "mapping_config_20250902_223125", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-02T22:31:49.160942", "table_name": "mapping_config_20250902_223149", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:31:52.052767", "table_name": "mapping_config_20250902_223152", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:31:52.848342", "table_name": "mapping_config_20250902_223152", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:31:53.445807", "table_name": "mapping_config_20250902_223153", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-02T22:31:54.737742", "table_name": "mapping_config_20250902_223154", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:53:05.953642", "table_name": "mapping_config_20250903_085305", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:53:25.351427", "table_name": "mapping_config_20250903_085325", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:53:26.930917", "table_name": "mapping_config_20250903_085326", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:53:33.735330", "table_name": "mapping_config_20250903_085333", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:53:43.307313", "table_name": "mapping_config_20250903_085343", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:53:59.137726", "table_name": "mapping_config_20250903_085359", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:54:02.937588", "table_name": "mapping_config_20250903_085402", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:54:07.664544", "table_name": "mapping_config_20250903_085407", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:54:15.387727", "table_name": "mapping_config_20250903_085415", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:54:17.920359", "table_name": "mapping_config_20250903_085417", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:54:20.820266", "table_name": "mapping_config_20250903_085420", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:54:22.187309", "table_name": "mapping_config_20250903_085422", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:54:46.420389", "table_name": "mapping_config_20250903_085446", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:54:48.330945", "table_name": "mapping_config_20250903_085448", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:54:52.119807", "table_name": "mapping_config_20250903_085452", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:54:55.377395", "table_name": "mapping_config_20250903_085455", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:54:58.592020", "table_name": "mapping_config_20250903_085458", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:55:18.537367", "table_name": "mapping_config_20250903_085518", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:55:45.446092", "table_name": "mapping_config_20250903_085545", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:56:00.906355", "table_name": "mapping_config_20250903_085600", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:56:01.707205", "table_name": "mapping_config_20250903_085601", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:56:06.936992", "table_name": "mapping_config_20250903_085606", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:56:10.245801", "table_name": "mapping_config_20250903_085610", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:56:18.469896", "table_name": "mapping_config_20250903_085618", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:56:24.484868", "table_name": "mapping_config_20250903_085624", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:56:44.787081", "table_name": "mapping_config_20250903_085644", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T08:57:42.318607", "table_name": "mapping_config_20250903_085742", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T08:57:50.834771", "table_name": "mapping_config_20250903_085750", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T08:58:22.113123", "table_name": "mapping_config_20250903_085822", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T08:58:27.020078", "table_name": "mapping_config_20250903_085826", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T09:55:52.424379", "table_name": "mapping_config_20250903_095552", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T09:56:05.283231", "table_name": "mapping_config_20250903_095605", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T09:56:09.255251", "table_name": "mapping_config_20250903_095609", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T09:56:11.054332", "table_name": "mapping_config_20250903_095611", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T09:56:13.885259", "table_name": "mapping_config_20250903_095613", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T09:56:22.867051", "table_name": "mapping_config_20250903_095622", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T09:56:28.937890", "table_name": "mapping_config_20250903_095628", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T09:56:36.226018", "table_name": "mapping_config_20250903_095636", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T09:57:04.786642", "table_name": "mapping_config_20250903_095704", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T09:57:12.854796", "table_name": "mapping_config_20250903_095712", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T09:57:36.110770", "table_name": "mapping_config_20250903_095736", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T09:57:41.830630", "table_name": "mapping_config_20250903_095741", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T09:57:49.949588", "table_name": "mapping_config_20250903_095749", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T09:58:01.025933", "table_name": "mapping_config_20250903_095800", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T09:58:09.147428", "table_name": "mapping_config_20250903_095809", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T09:58:35.499314", "table_name": "mapping_config_20250903_095835", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:05:30.163695", "table_name": "mapping_config_A岗职工_20250903_110530", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:05:46.694453", "table_name": "mapping_config_A岗职工_20250903_110546", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:05:52.711052", "table_name": "mapping_config_A岗职工_20250903_110552", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:05:59.213320", "table_name": "mapping_config_A岗职工_20250903_110559", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:06:07.676422", "table_name": "mapping_config_A岗职工_20250903_110607", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:06:12.853049", "table_name": "mapping_config_A岗职工_20250903_110612", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:06:25.138239", "table_name": "mapping_config_A岗职工_20250903_110625", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:06:41.338203", "table_name": "mapping_config_A岗职工_20250903_110641", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:06:44.979007", "table_name": "mapping_config_离休人员工资表_20250903_110644", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T11:06:53.191319", "table_name": "mapping_config_离休人员工资表_20250903_110653", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T11:06:56.217018", "table_name": "mapping_config_离休人员工资表_20250903_110656", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T11:06:59.463348", "table_name": "mapping_config_离休人员工资表_20250903_110659", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T11:07:04.376885", "table_name": "mapping_config_离休人员工资表_20250903_110704", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T11:07:21.629415", "table_name": "mapping_config_离休人员工资表_20250903_110721", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T11:07:24.262335", "table_name": "mapping_config_A岗职工_20250903_110724", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:07:30.503106", "table_name": "mapping_config_A岗职工_20250903_110730", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:20:50.496942", "table_name": "mapping_config_A岗职工_20250903_112050", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:20:55.067310", "table_name": "mapping_config_A岗职工_20250903_112055", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:20:58.026869", "table_name": "mapping_config_A岗职工_20250903_112057", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:21:04.519485", "table_name": "mapping_config_A岗职工_20250903_112104", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:21:06.424510", "table_name": "mapping_config_离休人员工资表_20250903_112106", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T11:21:09.577955", "table_name": "mapping_config_离休人员工资表_20250903_112109", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T11:21:31.523017", "table_name": "mapping_config_A岗职工_20250903_112131", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:21:39.084270", "table_name": "mapping_config_A岗职工_20250903_112138", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:21:43.834494", "table_name": "mapping_config_A岗职工_20250903_112143", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:28:26.085815", "table_name": "mapping_config_A岗职工_20250903_112826", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:28:32.371194", "table_name": "mapping_config_A岗职工_20250903_112832", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:28:35.983842", "table_name": "mapping_config_A岗职工_20250903_112835", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:28:40.216896", "table_name": "mapping_config_A岗职工_20250903_112840", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:28:45.016982", "table_name": "mapping_config_A岗职工_20250903_112844", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:28:51.599921", "table_name": "mapping_config_A岗职工_20250903_112851", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:29:01.666471", "table_name": "mapping_config_A岗职工_20250903_112901", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:31:15.413803", "table_name": "mapping_config_A岗职工_20250903_113115", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:31:20.360032", "table_name": "mapping_config_A岗职工_20250903_113120", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:31:23.118904", "table_name": "mapping_config_A岗职工_20250903_113123", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:31:27.376848", "table_name": "mapping_config_A岗职工_20250903_113127", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:31:30.466080", "table_name": "mapping_config_A岗职工_20250903_113130", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:31:32.526765", "table_name": "mapping_config_A岗职工_20250903_113132", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:31:40.582666", "table_name": "mapping_config_A岗职工_20250903_113140", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T11:31:46.639797", "table_name": "mapping_config_A岗职工_20250903_113146", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T12:06:15.170804", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T12:06:18.630333", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T12:06:23.052298", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T12:06:27.297414", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T12:06:34.540140", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T12:06:38.258354", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T12:06:42.340748", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T12:06:47.113655", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T12:23:28.860045", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T12:23:34.299165", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T12:23:39.114847", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T12:23:45.410236", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T12:23:51.282528", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T12:23:53.875818", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T12:23:57.401734", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T12:24:01.152087", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T15:32:49.029384", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T15:32:52.172929", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T15:32:57.735398", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T15:33:03.157152", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T15:33:06.594522", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T15:34:22.788958", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T15:34:24.214575", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T15:34:32.956171", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T15:34:43.362422", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T15:34:53.253250", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T15:34:56.159367", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T15:34:58.987578", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T15:35:01.487565", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T15:35:03.347009", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T15:35:10.956334", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T15:35:29.594439", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T16:17:42.825098", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:17:47.084115", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:17:50.304199", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:17:54.142938", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:17:57.783475", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:18:08.111896", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:18:18.070002", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T16:42:27.538324", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:42:31.189287", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:42:35.670618", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:42:38.920819", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:42:43.233116", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:42:53.427856", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:43:03.802409", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T16:55:38.730553", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:55:41.261721", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:55:43.511646", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:55:46.548480", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:55:50.486188", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:55:52.954953", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:55:56.314348", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:56:06.936343", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T16:56:15.219695", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T17:44:07.126213", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:44:10.668416", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:44:14.999186", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:44:18.472915", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:44:21.337252", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:44:25.307930", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:44:28.133229", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:44:32.446320", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:44:40.836357", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T17:55:36.936508", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:55:40.795935", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:55:47.623889", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:55:51.437649", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:55:56.342398", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:55:58.420486", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:56:01.186125", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:56:07.014347", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:56:13.757414", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T17:56:39.056062", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:56:39.118264", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:56:45.477613", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:56:48.555956", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:56:59.056783", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:57:20.081626", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T17:57:26.238172", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本\n离休费", "结余\n津贴", "生活\n补贴", "住房\n补贴", "物业\n补贴", "离休\n补贴", "护理费", "增发一次\n性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T18:54:20.154126", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T18:54:26.513438", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T18:54:31.584390", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T18:54:34.698676", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T18:54:38.848922", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T18:54:53.137875", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T18:54:59.121960", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T18:55:03.090708", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T18:55:13.106893", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T21:55:10.790897", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T21:55:13.306728", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T21:55:17.948539", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T21:55:19.136736", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T21:55:21.808073", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T21:55:28.551968", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T21:55:38.112239", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T21:55:43.643210", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T21:55:45.659041", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T22:29:25.542007", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T22:29:27.995163", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T22:29:31.778020", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T22:29:34.713897", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T22:29:38.987485", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T22:29:43.319242", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T22:29:50.482382", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T22:29:55.455240", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T22:30:01.923729", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T22:30:07.439269", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T22:30:14.423608", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T23:54:22.983030", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T23:54:25.729938", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T23:54:28.667428", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T23:54:31.089578", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T23:54:34.792817", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T23:54:42.111911", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T23:54:57.557182", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T23:55:04.867738", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T23:55:15.274295", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T23:55:18.554732", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T23:55:18.652192", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T23:55:28.245648", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T23:55:32.176091", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-03T23:55:36.890493", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T23:55:41.119912", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T23:55:47.355459", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T23:55:51.512044", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T23:55:53.326915", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T23:55:53.386630", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-03T23:55:58.261949", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T08:17:47.014794", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T08:17:49.582892", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T08:17:53.849114", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T08:17:56.005131", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T08:18:19.539209", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T08:18:24.333516", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T08:18:31.052163", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T08:18:37.412477", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T08:18:40.077484", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T08:18:44.826448", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T08:18:52.826262", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T08:18:55.106827", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T08:18:57.889850", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T08:19:07.075516", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T08:19:14.028781", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T08:19:18.388701", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T08:59:24.875530", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T08:59:29.589712", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T08:59:52.619323", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T09:00:05.155099", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T09:15:16.782064", "table_name": "mapping_config_A岗职工", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T09:15:32.167279", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T09:16:01.946672", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T09:16:26.800359", "table_name": "mapping_config_离休人员工资表", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T09:18:06.350690", "table_name": "mapping_config_全部在职人员工资表", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T09:18:12.319605", "table_name": "mapping_config_全部在职人员工资表", "action": "save", "field_count": 23, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T09:21:05.723998", "table_name": "mapping_config_退休人员工资表", "action": "save", "field_count": 27, "fields": ["序号", "人员代码", "姓名", "部门名称", "人员类别代码", "基本退休费", "津贴", "结余津贴", "离退休生活补贴", "护理费", "物业补贴", "住房补贴", "增资预付", "2016待遇调整", "2017待遇调整", "2018待遇调整", "2019待遇调整", "2020待遇调整", "2021待遇调整", "2022待遇调整", "2023待遇调整", "补发", "借支", "应发工资", "公积", "保险扣款", "备注"]}
{"timestamp": "2025-09-04T10:47:16.328556", "table_name": "mapping_config_default_sheet", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T10:47:31.054538", "table_name": "mapping_config_default_sheet", "action": "save", "field_count": 21, "fields": ["序号", "工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险"]}
{"timestamp": "2025-09-04T10:48:25.896926", "table_name": "mapping_config_default_sheet", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
{"timestamp": "2025-09-04T10:48:32.866656", "table_name": "mapping_config_default_sheet", "action": "save", "field_count": 16, "fields": ["序号", "人员代码", "姓名", "部门名称", "基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴", "离休补贴", "护理费", "增发一次性生活补贴", "补发", "合计", "借支", "备注"]}
